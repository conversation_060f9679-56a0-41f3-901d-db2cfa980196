from models.base_model import BaseModel, db

class ModalDataModel(BaseModel):
    """模态数据模型"""
    __tablename__ = 'modal_data'
    
    test_project_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('test_projects.id'), nullable=False, comment='测试项目ID')
    mode_order = db.Column(db.Integer, nullable=False, comment='模态阶次')
    direction = db.Column(db.String(100), comment='方向')
    frequency = db.Column(db.Numeric(10, 2), nullable=False, comment='频率(Hz)')
    damping_ratio = db.Column(db.Numeric(6, 2), comment='阻尼比')
    mode_shape_description = db.Column(db.Text, comment='模态振型描述')
    mode_shape_file = db.Column(db.String(255), comment='GIF动图文件路径')
    test_photo_file = db.Column(db.String(255), comment='测试照片文件路径')
    notes = db.Column(db.Text, comment='备注')
    updated_by = db.Column(db.String(50), comment='修改人员')
    
    # 关联关系
    test_project = db.relationship('TestProjectModel', backref='modal_data')
    
    def __repr__(self):
        return f'<ModalDataModel {self.mode_order}阶 {self.frequency}Hz>'
