from models.base_model import BaseModel, db

class AirtightnessImageModel(BaseModel):
    """气密性测试图片模型"""
    __tablename__ = 'airtightness_images'
    
    vehicle_model_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON><PERSON>('vehicle_models.id'), nullable=False, comment='车型ID')
    front_compartment_image = db.Column(db.String(255), comment='前舱图片路径')
    door_image = db.Column(db.String(255), comment='车门图片路径')
    tailgate_image = db.Column(db.String(255), comment='尾门图片路径')
    upload_date = db.Column(db.Date, comment='上传日期')
    notes = db.Column(db.Text, comment='备注')
    
    # 关联关系
    vehicle_model = db.relationship('VehicleModel', backref='airtightness_images')
    
    def __repr__(self):
        return f'<AirtightnessImageModel {self.vehicle_model_id}>'
    
    def get_images_dict(self):
        """获取图片路径字典"""
        return {
            'front_compartment': self.front_compartment_image,
            'doors': self.door_image,
            'tailgate': self.tailgate_image
        }
