from models.base_model import BaseModel, db

class VehicleModel(BaseModel):
    """车型模型"""
    __tablename__ = 'vehicle_models'
    
    vehicle_model_code = db.Column(db.String(50), unique=True, nullable=False, comment='车型代码')
    vehicle_model_name = db.Column(db.String(100), nullable=False, comment='车型名称')
    vin = db.Column(db.String(50), unique=True, nullable=False, comment='VIN码')
    drive_type = db.Column(db.String(30), comment='驱动类型')
    configuration = db.Column(db.String(200), comment='具体配置')
    production_year = db.Column(db.Integer, comment='生产年份')
    status = db.Column(db.Enum('active', 'inactive'), default='active', comment='状态')
    
    def __repr__(self):
        return f'<VehicleModel {self.vehicle_model_name}>'
