from models.base_model import BaseModel, db

class TestProjectModel(BaseModel):
    """测试项目模型"""
    __tablename__ = 'test_projects'
    
    project_code = db.Column(db.String(50), unique=True, nullable=False, comment='项目代码')
    project_name = db.Column(db.String(100), nullable=False, comment='项目名称')
    vehicle_model_id = db.Column(db.Integer, db.ForeignKey('vehicle_models.id'), nullable=False, comment='车辆ID')
    component_id = db.Column(db.Integer, db.ForeignKey('components.id'), comment='零件ID')
    test_type = db.Column(db.String(200), nullable=False, comment='测试类型')
    test_date = db.Column(db.Date, nullable=False, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), nullable=False, comment='测试工程师')
    test_condition = db.Column(db.String(200), comment='测试条件')
    test_status = db.Column(db.String(200), comment='测试状态')
    excitation_method = db.Column(db.String(100), comment='激励方式')
    notes = db.Column(db.Text, comment='备注')
    
    # 关联关系
    vehicle = db.relationship('VehicleModel', backref='test_projects')
    component = db.relationship('ComponentModel', backref='test_projects')
    
    def __repr__(self):
        return f'<TestProjectModel {self.project_name}>'
