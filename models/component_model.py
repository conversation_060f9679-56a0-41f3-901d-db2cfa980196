from models.base_model import BaseModel, db

class ComponentModel(BaseModel):
    """零部件模型"""
    __tablename__ = 'components'

    component_code = db.Column(db.String(50), unique=True, nullable=False, comment='零件代码')
    component_name = db.Column(db.String(100), nullable=False, comment='零件名称')
    category = db.Column(db.String(100), nullable=False, comment='主分类')
    sub_category = db.Column(db.String(50), nullable=False, comment='子分类')
    parent_id = db.Column(db.Integer, db.<PERSON>ey('components.id'), comment='父级零件ID')
    description = db.Column(db.Text, comment='描述')
    material = db.Column(db.String(100), comment='材料')
    weight = db.Column(db.Numeric(8, 3), comment='重量(kg)')

    def __repr__(self):
        return f'<ComponentModel {self.component_name}>'
