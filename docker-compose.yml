version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: nvh_mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: nvh_data
      MYSQL_USER: nvh_user
      MYSQL_PASSWORD: nvh_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./已有数据库表.md:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - nvh_network

  # NVH应用
  nvh_app:
    build: .
    container_name: nvh_app
    environment:
      FLASK_ENV: development
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_USER: nvh_user
      MYSQL_PASSWORD: nvh_password
      MYSQL_DATABASE: nvh_data
    ports:
      - "5000:5000"
    volumes:
      - ./static/uploads:/app/static/uploads
    depends_on:
      - mysql
    networks:
      - nvh_network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  nvh_network:
    driver: bridge
