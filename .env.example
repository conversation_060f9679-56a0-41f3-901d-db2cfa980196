# Flask环境配置
FLASK_ENV=development
SECRET_KEY=nvh-system-secret-key-2024

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=nvh_data

# Keycloak配置（已在config.py中配置，这里仅作示例）
# KEYCLOAK_FRONTEND_CLIENT_ID=front
# KEYCLOAK_FRONTEND_CLIENT_SECRET=frontend-secret
# KEYCLOAK_BACKEND_CLIENT_ID=backend
# KEYCLOAK_BACKEND_CLIENT_SECRET=8545c061-7cf7-41e5-b92b-e6769a6a75b8
# KEYCLOAK_SERVER_METADATA_URL=https://account-test.sgmw.com.cn/auth/realms/demo/.well-known/openid-configuration
