/* 模态搜索页面样式 */

/* 搜索表单样式 */
.search-form {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* 搜索结果表格样式 */
.search-results {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
}

.table-responsive {
    border-radius: 0.5rem;
}

.table th {
    background-color: #e9ecef;
    color: #495057;
    border: none;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
    text-align: center;
    border-color: #e9ecef;
}

/* 频率数值正常显示 */
.frequency-value {
    font-weight: normal;
    color: #495057;
}

/* 操作按钮样式 */
.btn-action {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

/* 模态振型查看弹窗样式 */
.modal-detail-content {
    max-height: 50vh;
    overflow-y: auto;
}

/* 振型信息行样式 */
.mode-shape-info-row {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.info-label {
    font-weight: 600;
    color: #495057;
    min-width: 50px;
}

.info-value {
    color: #212529;
    font-weight: 500;
}

/* 振型标签页样式 */
.mode-shape-tabs {
    margin-top: 1.5rem;
}

/* 标签页头部 - 左右分开布局 */
.tabs-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.tab-button {
    background: none;
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    flex: 1;
    text-align: center;
}

.tab-button:hover {
    color: #495057;
    background-color: rgba(0, 123, 255, 0.05);
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

/* 标签页内容区域 */
.tab-content-area {
    position: relative;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 图片容器样式 */
.image-container {
    padding: 1rem;
    text-align: center;
    min-height: 260px; /* 增加最小高度从300px到500px */
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.mode-shape-image {
    max-width: 100%;
    max-height: 260px; /* 增加最大高度从300px到500px */
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* 无图片占位符样式 */
.no-image-placeholder {
    text-align: center;
    color: #6c757d;
}

.no-image-placeholder i {
    opacity: 0.5;
}

.detail-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border-left: 4px solid #007bff;
}

.detail-section h6 {
    color: #007bff;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #6c757d;
    min-width: 120px;
}

.detail-value {
    color: #495057;
    text-align: right;
}

/* 图片预览样式 */
.image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 加载状态样式 */
.loading-state {
    text-align: center;
    padding: 2rem;
}

.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 搜索条件标签 */
.search-tags {
    margin-bottom: 1rem;
}

.search-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.search-tag .remove-tag {
    margin-left: 0.5rem;
    cursor: pointer;
    color: #6c757d;
}

.search-tag .remove-tag:hover {
    color: #dc3545;
}

/* 结果统计 */
.result-stats {
    background-color: #6c757d;
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 搜索标签页样式 */
.search-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-btn.active {
    background: white;
    color: #495057;
    border-bottom: 2px solid #007bff;
    font-weight: 600;
}

.tab-btn i {
    font-size: 14px;
}



/* 搜索表单样式 */
.tab-content {
    padding-top: 1.5rem;
}

/* 表单标签样式 */
.form-label {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}

/* 模态搜索表单行对齐 */
.row.align-items-center.g-1 {
    min-height: 38px;
    --bs-gutter-x: 0.25rem;
}

.row.align-items-center.g-1 .col-auto {
    padding-right: 0;
}

.row.align-items-center.g-1 .col {
    padding-left: 0.5rem;
}

/* 对标搜索表单行对齐 */
.row.align-items-start {
    min-height: 120px;
}

/* 提示文字样式 */
.form-text.text-center {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 对标按钮样式 */
.btn-sm.px-3 {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    font-size: 0.875rem;
    min-width: 100px;
}

.d-flex.flex-column.gap-2 {
    gap: 0.5rem !important;
}

/* 对标功能样式 */
.comparison-form .form-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 车型标签选择样式 */
.vehicle-search-container {
    position: relative;
}

.vehicle-tags-container {
    max-height: 120px;
    overflow-y: auto;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.5rem;
    background-color: #fff;
    min-height: 80px;
}

.vehicle-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    margin: 0.125rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.vehicle-tag:hover {
    background-color: #dee2e6;
    border-color: #007bff;
}

.vehicle-tag.selected {
    background-color: #007bff;
    color: white;
    border-color: #0056b3;
}

.vehicle-tag.selected:hover {
    background-color: #0056b3;
}

.selected-vehicles {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#clear-vehicles {
    color: #dc3545;
    text-decoration: none;
    font-size: 0.75rem;
}

#clear-vehicles:hover {
    color: #c82333;
    text-decoration: underline;
}

/* 紧凑间距样式 */
.comparison-form .row.align-items-center.mb-1,
.comparison-form .row.align-items-start.mb-1 {
    margin-bottom: 0.25rem !important;
}

.comparison-form .col-3,
.comparison-form .col-4 {
    padding-right: 0.25rem;
}

.comparison-form .col-8,
.comparison-form .col-9 {
    padding-left: 0.25rem;
}

/* 结果容器样式 */
.result-container {
    min-height: 400px;
}

/* 多选下拉框样式 */
.form-select[multiple] {
    min-height: 120px;
    padding: 0.5rem;
}

.form-select[multiple] option {
    padding: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.form-select[multiple] option:hover {
    background-color: #e9ecef;
}

.form-select[multiple] option:checked {
    background-color: #007bff;
    color: white;
}

/* 单选零件下拉框样式 */
#comparison-components {
    background-color: #fff;
    border: 1px solid #ced4da;
}

#comparison-components:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 对标表格样式 */
.comparison-table {
    font-size: 0.875rem;
}

.comparison-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 0.75rem 0.5rem;
}

.comparison-table td {
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
}

.comparison-table td:first-child {
    font-weight: 600;
    background-color: #f8f9fa;
    text-align: left;
    padding-left: 1rem;
}

/* 对标图表容器样式 */
.comparison-chart-container {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.comparison-chart-container h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.chart-wrapper {
    position: relative;
    height: 600px; /* 增加图表高度从400px到600px */
    cursor: pointer;
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.chart-wrapper canvas {
    cursor: pointer;
    width: 100% !important;
    height: 100% !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 对标结果卡片样式 */
.comparison-results {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
}

/* 对标空状态样式 */
.comparison-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.comparison-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 对标按钮样式 */
.btn-comparison {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-comparison:hover {
    background-color: #218838;
    border-color: #1e7e34;
    color: white;
}

.btn-comparison:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .chart-wrapper {
        height: 700px; /* 在大屏幕上进一步增加高度 */
    }

    .image-container {
        min-height: 600px;
    }

    .mode-shape-image {
        max-height: 600px;
    }
}

/* 自定义多选框样式 */
.custom-multiselect {
    position: relative;
    width: 100%;
}

.multiselect-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 38px;
    padding: 8px 12px;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.multiselect-trigger:hover {
    border-color: #007bff;
}

.multiselect-trigger.active {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.multiselect-placeholder {
    color: #495057;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.multiselect-placeholder.has-selection {
    color: #495057;
}

.multiselect-count {
    font-size: 12px;
    color: #6c757d;
    margin-right: 8px;
    white-space: nowrap;
}

.multiselect-arrow {
    color: #6c757d;
    font-size: 12px;
    transition: transform 0.2s ease;
}

.multiselect-arrow.rotated {
    transform: rotate(180deg);
}

.multiselect-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-height: 120px;
    overflow-y: auto;
    display: none;
}

.multiselect-dropdown.show {
    display: block;
    animation: dropdownFadeIn 0.2s ease;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.multiselect-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f8f9fa;
    height: 32px;
}

.multiselect-option:last-child {
    border-bottom: none;
}

.multiselect-option:hover {
    background-color: #f8f9fa;
}

.option-checkbox {
    margin-right: 8px;
    color: #6c757d;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.multiselect-option.selected .option-checkbox {
    color: #007bff;
}

.option-text {
    flex: 1;
    font-size: 14px;
    color: #495057;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 空状态和加载状态 */
.multiselect-empty,
.multiselect-loading {
    padding: 12px;
    text-align: center;
    color: #6c757d;
    font-size: 12px;
    font-style: italic;
}

/* 滚动条样式 */
.multiselect-dropdown::-webkit-scrollbar {
    width: 6px;
}

.multiselect-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.multiselect-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.multiselect-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 禁用状态 */
.custom-multiselect.disabled .multiselect-trigger {
    background-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    border-color: #ced4da;
}

.custom-multiselect.disabled .multiselect-trigger:hover {
    border-color: #ced4da;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .tab-btn {
        padding: 10px 15px;
        font-size: 13px;
    }

    .tab-btn i {
        font-size: 13px;
    }
    .search-form {
        padding: 1rem;
    }

    .comparison-form {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-action {
        padding: 0.125rem 0.5rem;
        font-size: 0.75rem;
    }

    /* 对标表格响应式 */
    .comparison-table {
        font-size: 0.75rem;
    }

    .comparison-table th,
    .comparison-table td {
        padding: 0.25rem;
    }

    /* 多选下拉框响应式 */
    .form-select[multiple] {
        min-height: 100px;
    }

    /* 图表容器响应式 */
    .chart-wrapper {
        height: 450px; /* 在中等屏幕上稍微减小高度 */
    }

    /* 图片容器响应式 */
    .image-container {
        min-height: 350px;
    }

    .mode-shape-image {
        max-height: 350px;
    }

    /* 标签页响应式 */
    .nav-tabs .nav-link {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }

    /* 表单响应式 */
    .form-label {
        margin-bottom: 0.25rem;
    }

    .row.align-items-start .col-4,
    .row.align-items-start .col-5 {
        margin-bottom: 0.25rem;
    }

    /* 对标按钮响应式 */
    .btn-sm.px-3 {
        min-width: 80px;
        font-size: 0.75rem;
    }

    /* 小屏幕下标签和输入框垂直排列 */
    @media (max-width: 576px) {
        .tab-btn {
            padding: 8px 12px;
            font-size: 12px;
            flex-direction: column;
            gap: 4px;
        }

        .tab-btn i {
            font-size: 16px;
        }
        .row.align-items-center.g-1,
        .row.align-items-start {
            flex-direction: column;
        }

        .row.align-items-center.g-1 .col-auto,
        .row.align-items-center.g-1 .col,
        .row.align-items-start .col-4,
        .row.align-items-start .col-8,
        .row.align-items-start .col-5,
        .row.align-items-start .col-7 {
            width: 100%;
            max-width: 100%;
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        .row.align-items-center.g-1 .col {
            padding-left: 0.75rem;
        }
    }

    /* 振型查看弹窗响应式 */
    .mode-shape-info-row {
        flex-direction: column;
        gap: 1rem;
    }

    .info-item {
        justify-content: space-between;
    }

    .info-label {
        min-width: auto;
    }

    .image-container {
        padding: 1rem;
        min-height: 250px; /* 在小屏幕上也保持合理的高度 */
    }

    .mode-shape-image {
        max-height: 250px;
    }

    /* 小屏幕图表响应式 */
    .chart-wrapper {
        height: 350px; /* 在小屏幕上减小高度但仍保持可读性 */
    }

    .tab-button {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .tabs-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tab-button {
        flex: none;
    }
}
