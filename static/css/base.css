/* 基础样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    padding-top: 56px; /* 导航栏高度 */
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    background-color: #2c3e50 !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    transition: transform 0.3s ease;
    width: 16.66667%; /* col-lg-2 width */
}

/* 侧边栏收起状态 */
.sidebar.collapsed {
    transform: translateX(-100%);
}

/* 侧边栏切换按钮样式 */
#sidebarToggle {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

#sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.sidebar .nav-link {
    color: #ecf0f1;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.sidebar .nav-link:hover {
    background-color: #34495e;
    color: #3498db;
}

.sidebar .nav-link.active {
    background-color: #3498db;
    color: white;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

.sidebar .nav-link i.me-2 {
    margin-right: 0.5rem !important;
}

.sidebar .nav-link i.ms-auto {
    margin-left: auto !important;
    width: auto;
}

/* 下拉箭头样式调整 - 移至右侧 */
.sidebar .nav-link[data-bs-toggle="collapse"] {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar .nav-link[data-bs-toggle="collapse"] .fa-chevron-down {
    transition: transform 0.3s ease;
    margin-left: auto !important;
}

.sidebar .nav-link[data-bs-toggle="collapse"]:not(.collapsed) .fa-chevron-down {
    transform: rotate(180deg);
}

/* 子菜单样式 */
.sidebar .collapse .nav-link {
    color: #bdc3c7;
    background-color: rgba(0, 0, 0, 0.1);
    border-left: 3px solid transparent;
}

.sidebar .collapse .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.2);
    color: #3498db;
    border-left-color: #3498db;
}

/* 主内容区域 */
main {
    padding-top: 20px;
    min-height: calc(100vh - 56px);
    background-color: #ffffff;
    transition: margin-left 0.3s ease, width 0.3s ease;
}

/* 侧边栏收起时主内容区域调整 */
.sidebar-collapsed main {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* 确保内容区域平滑过渡 */
.container-fluid .row {
    transition: all 0.3s ease;
}

/* 侧边栏收起时调整容器 */
.sidebar-collapsed .container-fluid .row {
    margin-left: 0;
}

/* 卡片样式 */
.card {
    border: 1px solid #e9ecef;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
    border-radius: 0.5rem;
}

.card:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #e9ecef;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}
/* 固定顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}
/* 欢迎页面样式 */
.welcome-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    margin: 2rem 0;
}

.welcome-section .card {
    transition: transform 0.3s ease;
}

.welcome-section .card:hover {
    transform: translateY(-5px);
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .sidebar {
        position: relative;
        top: 0;
        height: auto;
        width: 100%;
        transform: none;
    }

    .sidebar.collapsed {
        display: none;
    }

    main {
        padding-top: 10px;
    }

    .sidebar-collapsed main {
        margin-left: 0;
        width: 100%;
    }
}

@media (min-width: 768px) {
    .sidebar {
        width: 25%; /* col-md-3 width */
    }
}

@media (min-width: 992px) {
    .sidebar {
        width: 16.66667%; /* col-lg-2 width */
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 消息提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

/* 表单样式优化 */
.form-label {
    font-weight: 500;
    color: #495057;
}

.form-control:focus,
.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* 标签页导航样式 */
.tab-navigation-container {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0;
    margin: 0;
}

.tab-navigation-container .nav-tabs {
    border-bottom: none;
    margin: 0;
    padding: 0 1rem;
    background-color: transparent;
}

.tab-navigation-container .nav-tabs .nav-item {
    margin-bottom: 0;
}

.tab-navigation-container .nav-tabs .nav-link {
    border: none;
    border-radius: 0;
    color: #6c757d;
    padding: 0.75rem 1rem;
    background-color: transparent;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    max-width: 200px;
    overflow: hidden;
}

.tab-navigation-container .nav-tabs .nav-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
    border-color: transparent;
}

.tab-navigation-container .nav-tabs .nav-link.active {
    background-color: #ffffff;
    color: #007bff;
    border-bottom: 2px solid #007bff;
    font-weight: 500;
}

.tab-navigation-container .nav-tabs .nav-link .tab-title {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 0.25rem;
}

.tab-navigation-container .nav-tabs .nav-link .tab-close-btn {
    opacity: 0.6;
    transition: opacity 0.3s ease;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.tab-navigation-container .nav-tabs .nav-link .tab-close-btn:hover {
    opacity: 1;
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.tab-navigation-container .nav-tabs .nav-link:not(.active) .tab-close-btn {
    opacity: 0;
}

.tab-navigation-container .nav-tabs .nav-link:hover .tab-close-btn {
    opacity: 0.6;
}

/* 标签页内容样式 */
.tab-content {
    background-color: #ffffff;
    min-height: calc(100vh - 56px - 49px); /* 减去导航栏和标签页导航栏高度 */
}

.tab-content .tab-pane {
    display: none;
    padding: 1rem;
}

.tab-content .tab-pane.active {
    display: block;
}

/* 调整主内容区域样式以适应标签页 */
main {
    padding-top: 0; /* 移除原有的padding-top，因为标签页内容已有padding */
}

/* 标签页导航栏在侧边栏收起时的调整 */
.sidebar-collapsed .tab-navigation-container {
    margin-left: 0;
    width: 100%;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .tab-navigation-container .nav-tabs {
        padding: 0 0.5rem;
        overflow-x: auto;
        white-space: nowrap;
    }

    .tab-navigation-container .nav-tabs .nav-link {
        max-width: 150px;
        padding: 0.5rem 0.75rem;
    }

    .tab-content .tab-pane {
        padding: 0.5rem;
    }
}
