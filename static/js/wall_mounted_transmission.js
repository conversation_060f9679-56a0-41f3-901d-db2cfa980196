/**
 * 上墙法隔声量查询功能
 */

class WallMountedTransmissionQuery {
    constructor() {
        this.chart = null;
        this.currentData = null;
        this.availableWeights = [];
        this.resizeListenerAdded = false;
        this.init();
    }

    init() {
        console.log('WallMountedTransmissionQuery 开始初始化...');
        // 等待DOM元素准备好
        this.waitForElements(() => {
            console.log('DOM元素已准备好，开始加载数据...');
            this.bindEvents();
            this.loadParts();
        });
    }

    waitForElements(callback, maxAttempts = 50) {
        let attempts = 0;
        const checkElements = () => {
            const partSelect = document.getElementById('part-select');
            const materialSelect = document.getElementById('material-select');
            const weightSelect = document.getElementById('weight-select');
            const searchBtn = document.getElementById('search-btn');

            if (partSelect && materialSelect && weightSelect && searchBtn) {
                console.log('所有必需的DOM元素都已找到');
                callback();
            } else if (attempts < maxAttempts) {
                attempts++;
                console.log(`等待DOM元素... 尝试 ${attempts}/${maxAttempts}`);
                setTimeout(checkElements, 100);
            } else {
                console.error('等待DOM元素超时，缺少的元素:', {
                    partSelect: !!partSelect,
                    materialSelect: !!materialSelect,
                    weightSelect: !!weightSelect,
                    searchBtn: !!searchBtn
                });
            }
        };
        checkElements();
    }

    bindEvents() {
        // 表单提交事件
        $('#query-form').on('submit', (e) => {
            e.preventDefault();
            this.queryData();
        });

        // 重置按钮事件
        $('#reset-btn').on('click', () => {
            this.resetForm();
        });

        // 导出按钮事件
        $('#export-btn').on('click', () => {
            this.exportData();
        });

        // 查看图片按钮事件
        $('#view-image-btn').on('click', () => {
            this.viewTestImage();
        });

        // 选择框变化事件
        $('#part-select').on('change', () => {
            this.onPartChange();
        });

        $('#material-select').on('change', () => {
            this.onMaterialChange();
        });

        $('#weight-select').on('change', () => {
            this.updateButtonStates();
        });
    }

    async loadParts() {
        try {
            const response = await fetch('/wall_mounted_transmission/api/parts');
            const result = await response.json();
            
            if (result.code === 200) {
                this.populatePartSelect(result.data);
            } else {
                this.showError('加载零件列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载零件列表失败: ' + error.message);
        }
    }

    populatePartSelect(parts) {
        const select = $('#part-select');
        select.empty().append('<option value="">请选择零件</option>');
        
        parts.forEach(part => {
            select.append(`<option value="${part.name}">${part.name}</option>`);
        });
    }

    populateMaterialSelect(materials) {
        const select = $('#material-select');
        select.empty().append('<option value="">请选择材料</option>');
        
        materials.forEach(material => {
            select.append(`<option value="${material.name}">${material.name}</option>`);
        });
        
        select.prop('disabled', materials.length === 0);
    }

    populateWeightSelect(weights) {
        const select = $('#weight-select');
        select.empty().append('<option value="">请选择克重</option>');
        
        this.availableWeights = weights;
        weights.forEach(weightObj => {
            const weight = weightObj.weight;
            select.append(`<option value="${weight}">${weight}g/m²</option>`);
        });
        
        select.prop('disabled', weights.length === 0);
    }

    async onPartChange() {
        const partName = $('#part-select').val();
        const materialSelect = $('#material-select');
        const weightSelect = $('#weight-select');
        
        // 重置材料和克重选择框
        materialSelect.empty().append('<option value="">请选择材料</option>').prop('disabled', !partName);
        weightSelect.empty().append('<option value="">请选择克重</option>').prop('disabled', true);
        this.updateButtonStates();
        
        if (!partName) return;
        
        try {
            const response = await fetch(`/wall_mounted_transmission/api/materials?part_name=${encodeURIComponent(partName)}`);
            const result = await response.json();
            
            if (result.code === 200) {
                this.populateMaterialSelect(result.data);
            } else {
                this.showError('加载材料列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载材料列表失败: ' + error.message);
        }
    }

    async onMaterialChange() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weightSelect = $('#weight-select');
        
        // 重置克重选择框
        weightSelect.empty().append('<option value="">请选择克重</option>').prop('disabled', !materialName);
        this.updateButtonStates();
        
        if (!partName || !materialName) return;
        
        try {
            const response = await fetch(`/wall_mounted_transmission/api/weights?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}`);
            const result = await response.json();
            
            if (result.code === 200) {
                this.populateWeightSelect(result.data);
            } else {
                this.showError('加载克重列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载克重列表失败: ' + error.message);
        }
    }

    updateButtonStates() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();
        
        const hasAllConditions = partName && materialName && weight;
        $('#query-btn').prop('disabled', !hasAllConditions);
        $('#export-btn').prop('disabled', !this.currentData);
    }

    async queryData() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();
        
        if (!partName || !materialName || !weight) {
            this.showError('请选择完整的查询条件');
            return;
        }
        
        this.showLoading(true);
        this.hideError();
        
        try {
            const response = await fetch(`/wall_mounted_transmission/api/transmission_data?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${encodeURIComponent(weight)}`);
            const result = await response.json();
            
            if (result.code === 200) {
                this.currentData = result.data;
                this.displayData(result.data);
                this.updateButtonStates();
            } else {
                this.showError(result.message);
                this.hideDataDisplay();
            }
        } catch (error) {
            this.showError('查询失败: ' + error.message);
            this.hideDataDisplay();
        } finally {
            this.showLoading(false);
        }
    }

    displayData(data) {
        this.hideEmptyState();
        this.showDataDisplay();
        this.displayBasicInfo(data);
        this.displayTable(data);
        this.displayChart(data);
    }

    displayBasicInfo(data) {
        const basicInfo = $('#basic-info');
        basicInfo.empty();
        
        const info = [
            { label: '零件名称', value: data.part_name || '-' },
            { label: '材料名称', value: data.material_name || '-' },
            { label: '厚度', value: data.thickness ? `${data.thickness}mm` : '-' },
            { label: '克重', value: data.weight ? `${data.weight}g/m²` : '-' },
            { label: '厂家', value: data.manufacturer_name || '-' },
            { label: '测试机构', value: data.test_institution || '-' },
            { label: '测试日期', value: data.test_date || '-' },
            { label: '测试地点', value: data.test_location || '-' },
            { label: '测试工程师', value: data.test_engineer || '-' }
        ];
        
        info.forEach(item => {
            basicInfo.append(`
                <div class="col-md-3 mb-2">
                    <strong>${item.label}:</strong> ${item.value}
                </div>
            `);
        });
    }

    displayTable(data) {
        const table = $('#data-table');
        const header = $('#frequency-header');
        const tbody = table.find('tbody');
        
        // 清空现有内容
        header.find('th:not(:first)').remove();
        tbody.empty();
        
        // 获取频率数据
        const testData = data.test_frequency_data || {};
        const targetData = data.target_frequency_data || {};
        const frequencies = ['125Hz', '160Hz', '200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz',
                           '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz',
                           '6300Hz', '8000Hz', '10000Hz'];
        
        // 添加频率列标题
        frequencies.forEach(freq => {
            header.append(`<th class="text-center" style="width: 80px; min-width: 80px; font-size: 0.85em; white-space: nowrap;">${freq}</th>`);
        });
        
        // 添加测试值行
        let testRow = '<tr><td class="fw-bold">测试</td>';
        frequencies.forEach(freq => {
            const value = testData[freq];
            const displayValue = value !== null && value !== undefined ? value.toFixed(2) : '-';
            testRow += `<td>${displayValue}</td>`;
        });
        testRow += '</tr>';
        tbody.append(testRow);
        
        // 添加目标值行
        let targetRow = '<tr><td class="fw-bold">目标</td>';
        frequencies.forEach(freq => {
            const value = targetData[freq];
            const displayValue = value !== null && value !== undefined ? value.toFixed(2) : '-';
            targetRow += `<td>${displayValue}</td>`;
        });
        targetRow += '</tr>';
        tbody.append(targetRow);
        
        // 添加达标状态行
        let statusRow = '<tr><td class="fw-bold">状态</td>';
        frequencies.forEach(freq => {
            const testValue = testData[freq];
            const targetValue = targetData[freq];
            
            if (testValue !== null && testValue !== undefined && 
                targetValue !== null && targetValue !== undefined) {
                const isPass = testValue >= targetValue;
                const statusClass = isPass ? 'text-success' : 'text-danger';
                const statusIcon = isPass ? 'fas fa-check' : 'fas fa-times';
                const statusText = isPass ? '达标' : '不达标';
                statusRow += `<td class="${statusClass}"><i class="${statusIcon} me-1"></i>${statusText}</td>`;
            } else {
                statusRow += '<td>-</td>';
            }
        });
        statusRow += '</tr>';
        tbody.append(statusRow);
    }

    displayChart(data) {
        const container = document.getElementById('chart-container');

        if (this.chart) {
            this.chart.dispose();
        }

        if (typeof echarts === 'undefined') {
            console.error('ECharts未加载');
            return;
        }

        // 确保容器可见后再初始化图表
        setTimeout(() => {
            // 检查容器是否可见
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn('Chart container is not visible, retrying...');
                setTimeout(() => this.displayChart(data), 100);
                return;
            }

            this.chart = echarts.init(container);

        // 准备数据
        const frequencies = ['125Hz', '160Hz', '200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz',
                           '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz',
                           '6300Hz', '8000Hz', '10000Hz'];

        const testData = data.test_frequency_data || {};
        const targetData = data.target_frequency_data || {};

        const testValues = frequencies.map(freq => testData[freq]);
        const targetValues = frequencies.map(freq => targetData[freq]);

        const option = {
            title: {
                text: '上墙法隔声量对比',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                },
                formatter: function(params) {
                    let result = `<strong>${params[0].axisValue}</strong><br/>`;
                    params.forEach(param => {
                        if (param.value !== null && param.value !== undefined) {
                            result += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)}dB<br/>`;
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: ['测试值', '目标值'],
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: frequencies,
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                },
                name: '频率',
                nameLocation: 'middle',
                nameGap: 35
            },
            yAxis: {
                type: 'value',
                name: '隔声量(dB)',
                nameLocation: 'middle',
                nameGap: 40,
                axisLabel: {
                    formatter: '{value}dB'
                }
            },
            series: [
                {
                    name: '测试值',
                    type: 'line',
                    data: testValues,
                    lineStyle: {
                        color: '#007bff',
                        width: 2
                    },
                    itemStyle: {
                        color: '#007bff'
                    },
                    symbol: 'circle',
                    symbolSize: 6,
                    connectNulls: false
                },
                {
                    name: '目标值',
                    type: 'line',
                    data: targetValues,
                    lineStyle: {
                        color: '#28a745',
                        width: 2,
                        type: 'dashed'
                    },
                    itemStyle: {
                        color: '#28a745'
                    },
                    symbol: 'diamond',
                    symbolSize: 6,
                    connectNulls: false
                }
            ]
        };

        this.chart.setOption(option);

        }, 100);

        // 响应式调整 - 移除重复的事件监听器
        if (!this.resizeListenerAdded) {
            window.addEventListener('resize', () => {
                if (this.chart) {
                    this.chart.resize();
                }
            });
            this.resizeListenerAdded = true;
        }
    }

    async exportData() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();

        if (!partName || !materialName || !weight) {
            this.showError('请选择完整的查询条件');
            return;
        }

        try {
            const url = `/wall_mounted_transmission/api/export_csv?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${encodeURIComponent(weight)}`;
            window.open(url, '_blank');
        } catch (error) {
            this.showError('导出失败: ' + error.message);
        }
    }

    async viewTestImage() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();

        if (!partName || !materialName || !weight) {
            this.showError('请选择完整的查询条件');
            return;
        }

        try {
            const response = await fetch(`/wall_mounted_transmission/api/test_image?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${encodeURIComponent(weight)}`);
            const result = await response.json();

            if (result.code === 200) {
                this.showImageModal(result.data);
            } else {
                this.showError('获取测试图片失败: ' + result.message);
            }
        } catch (error) {
            this.showError('获取测试图片失败: ' + error.message);
        }
    }

    showImageModal(imageInfo) {
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        const img = $('#test-image');
        const placeholder = $('#image-placeholder');
        const infoTable = $('#image-info');

        // 清空信息表格
        infoTable.empty();

        // 显示图片或占位符
        if (imageInfo.image_path) {
            img.attr('src', imageInfo.image_path).show();
            placeholder.hide();
        } else {
            img.hide();
            placeholder.show();
        }

        // 填充测试信息
        const info = [
            { label: '零件名称', value: imageInfo.part_name || '-' },
            { label: '材料名称', value: imageInfo.material_name || '-' },
            { label: '厚度', value: imageInfo.thickness ? `${imageInfo.thickness}mm` : '-' },
            { label: '克重', value: imageInfo.weight ? `${imageInfo.weight}g/m²` : '-' },
            { label: '厂家', value: imageInfo.manufacturer_name || '-' },
            { label: '测试机构', value: imageInfo.test_institution || '-' },
            { label: '测试日期', value: imageInfo.test_date || '-' },
            { label: '测试地点', value: imageInfo.test_location || '-' },
            { label: '测试工程师', value: imageInfo.test_engineer || '-' },
            { label: '备注', value: imageInfo.remarks || '-' }
        ];

        info.forEach(item => {
            infoTable.append(`
                <tr>
                    <td class="fw-bold">${item.label}</td>
                    <td>${item.value}</td>
                </tr>
            `);
        });

        modal.show();
    }

    resetForm() {
        $('#part-select').val('').trigger('change');
        $('#material-select').empty().append('<option value="">请选择材料</option>').prop('disabled', true);
        $('#weight-select').empty().append('<option value="">请选择克重</option>').prop('disabled', true);
        this.currentData = null;
        this.hideDataDisplay();
        this.showEmptyState();
        this.hideError();
        this.updateButtonStates();
    }

    showDataDisplay() {
        $('#data-display').show();
    }

    hideDataDisplay() {
        $('#data-display').hide();
    }

    showEmptyState() {
        $('#empty-state').show();
    }

    hideEmptyState() {
        $('#empty-state').hide();
    }

    showLoading(show) {
        if (show) {
            $('#loading-indicator').show();
            $('#empty-state').hide();
        } else {
            $('#loading-indicator').hide();
        }
    }

    showError(message) {
        $('#error-message').text(message);
        $('#error-alert').show();
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    hideError() {
        $('#error-alert').hide();
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    const currentPath = window.location.pathname;

    if (currentPath.includes('/wall_mounted_transmission/transmission_loss_query')) {
        window.wallMountedTransmissionQuery = new WallMountedTransmissionQuery();
    }
});

// 为标签页环境提供初始化函数
window.initializeWallMountedTransmissionQuery = function() {
    console.log('initializeWallMountedTransmissionQuery 被调用');

    // 等待一段时间确保DOM完全加载
    setTimeout(() => {
        if (typeof WallMountedTransmissionQuery !== 'undefined') {
            console.log('创建 WallMountedTransmissionQuery 实例');
            window.wallMountedTransmissionQuery = new WallMountedTransmissionQuery();
        } else {
            console.error('WallMountedTransmissionQuery 未定义');
        }
    }, 200);
};
