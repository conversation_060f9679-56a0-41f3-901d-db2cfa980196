/**
 * 基础JavaScript功能
 */

// 全局标签页管理器
let tabManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 标签页管理器类
 */
class TabManager {
    constructor() {
        this.tabs = new Map(); // 存储标签页信息
        this.activeTabId = 'home';
        this.tabCounter = 0;
        this.urlSyncEnabled = true; // 是否启用URL同步
        this.init();
    }

    init() {
        // 创建首页标签
        this.createHomeTab();

        // 绑定侧边栏链接事件
        this.bindSidebarLinks();

        // 绑定浏览器前进/后退事件
        this.bindPopStateEvent();

        // 处理当前URL（如果不是首页）
        this.handleInitialURL();
    }

    createHomeTab() {
        const homeTab = {
            id: 'home',
            title: '首页',
            icon: 'fas fa-home',
            url: '/',
            closable: false,
            loaded: true
        };

        this.tabs.set('home', homeTab);
        this.renderHomeTab(homeTab);
        this.switchTab('home');
    }

    renderHomeTab(tab) {
        const tabsContainer = document.getElementById('mainTabs');
        const homeContent = document.getElementById('home-content');
        const originalContent = document.getElementById('original-content');

        if (!tabsContainer) return;

        // 创建首页标签页导航项
        const tabNavItem = document.createElement('li');
        tabNavItem.className = 'nav-item';
        tabNavItem.innerHTML = `
            <a class="nav-link active" data-tab-id="${tab.id}" href="#" role="tab">
                <i class="${tab.icon} me-1"></i>
                <span class="tab-title">${tab.title}</span>
            </a>
        `;

        tabsContainer.appendChild(tabNavItem);

        // 将原始内容移动到首页标签中
        if (homeContent && originalContent) {
            homeContent.innerHTML = originalContent.innerHTML;
            originalContent.remove(); // 移除原始内容容器
        }

        // 绑定标签页点击事件
        const tabLink = tabNavItem.querySelector('.nav-link');
        tabLink.addEventListener('click', (e) => {
            e.preventDefault();
            this.switchTab(tab.id);
        });
    }

    bindSidebarLinks() {
        // 绑定侧边栏链接点击事件
        const sidebarLinks = document.querySelectorAll('.sidebar .nav-link[href]:not([data-bs-toggle])');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const url = link.getAttribute('href');
                const title = link.textContent.trim();
                const icon = link.querySelector('i')?.className || 'fas fa-file';

                // 跳过首页链接，因为已经有首页标签
                if (url === '/' || url === '/index') {
                    this.switchTab('home');
                    return;
                }

                this.openTab(url, title, icon);
            });
        });
    }

    openTab(url, title, icon) {
        // 生成标签页ID
        const tabId = this.generateTabId(url);

        // 如果标签页已存在，直接切换
        if (this.tabs.has(tabId)) {
            this.switchTab(tabId);
            return;
        }

        // 创建新标签页
        const tab = {
            id: tabId,
            title: title,
            icon: icon,
            url: url,
            closable: true,
            loaded: false
        };

        this.tabs.set(tabId, tab);
        this.renderTab(tab);
        this.switchTab(tabId);
        this.loadTabContent(tab);
    }

    generateTabId(url) {
        // 基于URL生成唯一的标签页ID
        return 'tab-' + url.replace(/[^a-zA-Z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
    }

    renderTab(tab) {
        const tabsContainer = document.getElementById('mainTabs');
        const contentContainer = document.getElementById('tabContentContainer');

        if (!tabsContainer || !contentContainer) return;

        // 创建标签页导航项
        const tabNavItem = document.createElement('li');
        tabNavItem.className = 'nav-item';
        tabNavItem.innerHTML = `
            <a class="nav-link" data-tab-id="${tab.id}" href="#" role="tab">
                <i class="${tab.icon} me-1"></i>
                <span class="tab-title">${tab.title}</span>
                ${tab.closable ? '<i class="fas fa-times ms-2 tab-close-btn" data-tab-id="' + tab.id + '"></i>' : ''}
            </a>
        `;

        tabsContainer.appendChild(tabNavItem);

        // 创建标签页内容容器
        const tabContent = document.createElement('div');
        tabContent.className = 'tab-pane';
        tabContent.id = `tab-content-${tab.id}`;
        tabContent.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载页面内容...</p>
            </div>
        `;

        contentContainer.appendChild(tabContent);

        // 绑定标签页点击事件
        const tabLink = tabNavItem.querySelector('.nav-link');
        tabLink.addEventListener('click', (e) => {
            e.preventDefault();
            this.switchTab(tab.id);
        });

        // 绑定关闭按钮事件
        if (tab.closable) {
            const closeBtn = tabNavItem.querySelector('.tab-close-btn');
            closeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.closeTab(tab.id);
            });
        }
    }

    switchTab(tabId) {
        // 移除所有活跃状态
        document.querySelectorAll('#mainTabs .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });

        // 激活目标标签页
        const targetTabLink = document.querySelector(`#mainTabs .nav-link[data-tab-id="${tabId}"]`);
        const targetTabContent = document.getElementById(`tab-content-${tabId}`);

        if (targetTabLink && targetTabContent) {
            targetTabLink.classList.add('active');
            targetTabContent.classList.add('active');
            this.activeTabId = tabId;

            // 更新侧边栏高亮
            this.updateSidebarHighlight(tabId);

            // 同步更新URL
            this.updateURL(tabId);
        }
    }

    closeTab(tabId) {
        if (tabId === 'home' || !this.tabs.has(tabId)) return;

        const tab = this.tabs.get(tabId);

        // 移除DOM元素
        const tabNavItem = document.querySelector(`#mainTabs .nav-link[data-tab-id="${tabId}"]`)?.parentElement;
        const tabContent = document.getElementById(`tab-content-${tabId}`);

        if (tabNavItem) tabNavItem.remove();
        if (tabContent) tabContent.remove();

        // 从Map中移除
        this.tabs.delete(tabId);

        // 如果关闭的是当前活跃标签页，切换到其他标签页
        if (this.activeTabId === tabId) {
            const remainingTabs = Array.from(this.tabs.keys());
            const nextTabId = remainingTabs[remainingTabs.length - 1] || 'home';
            this.switchTab(nextTabId);
        }
    }

    async loadTabContent(tab) {
        if (tab.loaded) return;

        const contentContainer = document.getElementById(`tab-content-${tab.id}`);
        if (!contentContainer) return;

        try {
            // 构建API URL
            const apiUrl = this.getContentApiUrl(tab.url);

            console.log(`加载标签页内容: ${tab.id}, URL: ${apiUrl}`);

            // 发送请求获取页面内容
            const result = await request.get(apiUrl);

            // 更新内容
            contentContainer.innerHTML = result.data.content;
            tab.loaded = true;

            console.log(`标签页内容加载完成: ${tab.id}`);

            // 等待DOM更新完成后执行初始化脚本
            setTimeout(() => {
                this.initializePageScripts(tab);
            }, 300);

        } catch (error) {
            console.error('加载标签页内容失败:', error);
            contentContainer.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5 class="text-muted">加载失败</h5>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-primary" onclick="tabManager.reloadTab('${tab.id}')">
                        <i class="fas fa-redo me-1"></i>重新加载
                    </button>
                </div>
            `;
        }
    }

    getContentApiUrl(url) {
        // 将页面URL转换为API URL
        const apiMapping = {
            // 气密性测试
            '/airtightness/comparison': '/airtightness/api/comparison-content',
            '/airtightness/images': '/airtightness/api/images-content',
            // 吸隔声模块
            '/sound_insulation/area_comparison': '/sound_insulation/api/area-comparison-content',
            '/sound_insulation/vehicle_insulation': '/sound_insulation/api/vehicle-insulation-content',
            '/sound_insulation/vehicle_reverberation': '/sound_insulation/api/vehicle-reverberation-content',
            '/sound_absorption/coefficient_query': '/sound_absorption/api/coefficient-query-content',
            '/sound_transmission/transmission_loss_query': '/sound_transmission/api/transmission-loss-query-content',
            '/wall_mounted_transmission/transmission_loss_query': '/wall_mounted_transmission/api/transmission-loss-query-content',
            '/material_porosity_flow_resistance/query': '/material_porosity_flow_resistance/api/query-content'
        };

        return apiMapping[url] || url + '/content';
    }

    initializePageScripts(tab) {
        console.log(`初始化页面脚本: ${tab.id}, URL: ${tab.url}`);

        // 根据标签页类型初始化相应的脚本
        if (tab.url.includes('/airtightness/comparison')) {
            console.log('初始化气密性对比页面脚本');
            if (typeof window.initializeAirtightnessComparison === 'function') {
                console.log('调用 initializeAirtightnessComparison');
                window.initializeAirtightnessComparison();
            } else {
                console.error('initializeAirtightnessComparison 函数未找到');
            }
        } else if (tab.url.includes('/airtightness/images')) {
            console.log('初始化气密性图片页面脚本');
            if (typeof window.initializeAirtightnessImages === 'function') {
                console.log('调用 initializeAirtightnessImages');
                window.initializeAirtightnessImages();
            } else {
                console.error('initializeAirtightnessImages 函数未找到');
            }
        } else if (tab.url.includes('/sound_insulation/area_comparison')) {
            console.log('初始化区域隔声量对比页面脚本');
            if (typeof window.initializeSoundInsulationArea === 'function') {
                console.log('调用 initializeSoundInsulationArea');
                window.initializeSoundInsulationArea();
            } else {
                console.error('initializeSoundInsulationArea 函数未找到');
            }
        } else if (tab.url.includes('/sound_insulation/vehicle_insulation')) {
            console.log('初始化车型隔声量对比页面脚本');
            if (typeof window.initializeVehicleInsulation === 'function') {
                console.log('调用 initializeVehicleInsulation');
                window.initializeVehicleInsulation();
            } else {
                console.error('initializeVehicleInsulation 函数未找到');
            }
        } else if (tab.url.includes('/sound_insulation/vehicle_reverberation')) {
            console.log('初始化车型混响时间对比页面脚本');
            if (typeof window.initializeVehicleReverberation === 'function') {
                console.log('调用 initializeVehicleReverberation');
                window.initializeVehicleReverberation();
            } else {
                console.error('initializeVehicleReverberation 函数未找到');
            }
        } else if (tab.url.includes('/sound_absorption/coefficient_query')) {
            console.log('初始化垂直入射吸音系数查询页面脚本');
            if (typeof window.initializeSoundAbsorptionQuery === 'function') {
                console.log('调用 initializeSoundAbsorptionQuery');
                window.initializeSoundAbsorptionQuery();
            } else {
                console.error('initializeSoundAbsorptionQuery 函数未找到');
            }
        } else if (tab.url.includes('/sound_transmission/transmission_loss_query')) {
            console.log('初始化垂直入射法隔声量查询页面脚本');
            if (typeof window.initializeSoundTransmissionQuery === 'function') {
                window.initializeSoundTransmissionQuery();
            }
        } else if (tab.url.includes('/wall_mounted_transmission/transmission_loss_query')) {
            console.log('初始化上墙法隔声量查询页面脚本');
            if (typeof window.initializeWallMountedTransmissionQuery === 'function') {
                window.initializeWallMountedTransmissionQuery();
            }
        } else if (tab.url.includes('/material_porosity_flow_resistance/query')) {
            console.log('初始化孔隙率流阻查询页面脚本');
            if (typeof window.initializeMaterialPorosityFlowResistanceQuery === 'function') {
                window.initializeMaterialPorosityFlowResistanceQuery();
            }
        }
    }

    updateSidebarHighlight(tabId) {
        // 移除所有侧边栏高亮
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // 根据标签页ID高亮对应的侧边栏项
        const tab = this.tabs.get(tabId);
        if (tab && tab.url) {
            const sidebarLink = document.querySelector(`.sidebar .nav-link[href="${tab.url}"]`);
            if (sidebarLink) {
                sidebarLink.classList.add('active');

                // 展开父级折叠菜单
                const parentCollapse = sidebarLink.closest('.collapse');
                if (parentCollapse) {
                    parentCollapse.classList.add('show');
                }
            }
        }
    }

    reloadTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (tab) {
            tab.loaded = false;
            this.loadTabContent(tab);
        }
    }

    /**
     * URL同步相关方法
     */

    // 更新浏览器URL
    updateURL(tabId) {
        if (!this.urlSyncEnabled) return;

        const tab = this.tabs.get(tabId);
        if (tab) {
            const url = tab.url || '/';

            // 使用pushState更新URL，不刷新页面
            if (window.location.pathname !== url) {
                window.history.pushState({ tabId: tabId }, '', url);
                console.log(`URL已更新为: ${url}`);
            }
        }
    }

    // 绑定浏览器前进/后退事件
    bindPopStateEvent() {
        window.addEventListener('popstate', (event) => {
            console.log('popstate事件触发:', event.state);

            // 暂时禁用URL同步，避免循环更新
            this.urlSyncEnabled = false;

            if (event.state && event.state.tabId) {
                // 如果有保存的标签页状态，切换到对应标签页
                this.switchTab(event.state.tabId);
            } else {
                // 否则根据当前URL解析并打开对应标签页
                this.handleCurrentURL();
            }

            // 重新启用URL同步
            setTimeout(() => {
                this.urlSyncEnabled = true;
            }, 100);
        });
    }

    // 处理初始URL（页面加载时）
    handleInitialURL() {
        const currentPath = window.location.pathname;
        console.log('处理初始URL:', currentPath);

        // 如果不是首页，尝试打开对应的标签页
        if (currentPath !== '/' && currentPath !== '/index') {
            this.openTabFromURL(currentPath);
        }
    }

    // 处理当前URL（浏览器前进/后退时）
    handleCurrentURL() {
        const currentPath = window.location.pathname;
        console.log('处理当前URL:', currentPath);

        if (currentPath === '/' || currentPath === '/index') {
            // 切换到首页
            this.switchTab('home');
        } else {
            // 尝试打开对应的标签页
            this.openTabFromURL(currentPath);
        }
    }

    // 根据URL打开标签页
    openTabFromURL(url) {
        // 检查是否已经有对应的标签页
        const existingTabId = this.findTabByURL(url);
        if (existingTabId) {
            this.switchTab(existingTabId);
            return;
        }

        // 根据URL获取标签页信息
        const tabInfo = this.getTabInfoFromURL(url);
        if (tabInfo) {
            // 暂时禁用URL同步，避免重复更新
            this.urlSyncEnabled = false;

            this.openTab(tabInfo.url, tabInfo.title, tabInfo.icon);

            // 重新启用URL同步
            setTimeout(() => {
                this.urlSyncEnabled = true;
            }, 100);
        } else {
            console.warn('无法识别的URL:', url);
            // 如果无法识别URL，切换到首页
            this.switchTab('home');
        }
    }

    // 根据URL查找已存在的标签页
    findTabByURL(url) {
        for (const [tabId, tab] of this.tabs) {
            if (tab.url === url) {
                return tabId;
            }
        }
        return null;
    }

    // 根据URL获取标签页信息
    getTabInfoFromURL(url) {
        // URL到标签页信息的映射
        const urlMapping = {
            // 气密性测试
            '/airtightness/comparison': {
                title: '泄漏量对比',
                icon: 'fas fa-chart-bar',
                url: '/airtightness/comparison'
            },
            '/airtightness/images': {
                title: '测试图片',
                icon: 'fas fa-images',
                url: '/airtightness/images'
            },
            // 模态数据
            '/modal/search': {
                title: '模态数据',
                icon: 'fas fa-wave-square',
                url: '/modal/search'
            },
            // 吸隔声模块
            '/sound_insulation/area_comparison': {
                title: '区域隔声量',
                icon: 'fas fa-chart-line',
                url: '/sound_insulation/area_comparison'
            },
            '/sound_insulation/vehicle_insulation': {
                title: '车型隔声量对比',
                icon: 'fas fa-car',
                url: '/sound_insulation/vehicle_insulation'
            },
            '/sound_insulation/vehicle_reverberation': {
                title: '车型混响时间对比',
                icon: 'fas fa-wave-square',
                url: '/sound_insulation/vehicle_reverberation'
            },
            '/sound_absorption/coefficient_query': {
                title: '垂直入射吸音系数查询',
                icon: 'fas fa-search',
                url: '/sound_absorption/coefficient_query'
            },
            '/sound_transmission/transmission_loss_query': {
                title: '垂直入射法隔声量查询',
                icon: 'fas fa-shield-alt',
                url: '/sound_transmission/transmission_loss_query'
            },
            '/wall_mounted_transmission/transmission_loss_query': {
                title: '上墙法隔声量查询',
                icon: 'fas fa-wall-brick',
                url: '/wall_mounted_transmission/transmission_loss_query'
            },
            '/material_porosity_flow_resistance/query': {
                title: '孔隙率流阻查询',
                icon: 'fas fa-filter',
                url: '/material_porosity_flow_resistance/query'
            }
        };

        return urlMapping[url] || null;
    }
}

/**
 * 初始化标签页管理器
 */
function initializeTabManager() {
    // 只有在存在标签页容器时才初始化
    if (document.getElementById('mainTabs')) {
        tabManager = new TabManager();
    }
}

/**
 * 初始化应用
 */
function initializeApp() {
    // 初始化标签页管理器
    initializeTabManager();

    // 初始化侧边栏
    initializeSidebar();

    // 初始化侧边栏切换功能
    initializeSidebarToggle();

    // 初始化键盘快捷键
    initializeKeyboardShortcuts();

    // 初始化用户信息
    initializeUserInfo();

    // 设置当前页面的导航高亮
    highlightCurrentNav();
}

/**
 * 初始化侧边栏
 */
function initializeSidebar() {
    // 处理折叠菜单
    const collapseElements = document.querySelectorAll('[data-bs-toggle="collapse"]');
    collapseElements.forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const targetSelector = this.getAttribute('data-bs-target');
            const target = document.querySelector(targetSelector);
            if (target) {
                // 检查是否已经有Collapse实例
                let collapse = bootstrap.Collapse.getInstance(target);
                if (!collapse) {
                    collapse = new bootstrap.Collapse(target, {
                        toggle: false
                    });
                }
                collapse.toggle();
            }
        });
    });
}

/**
 * 初始化侧边栏切换功能
 */
function initializeSidebarToggle() {
    const toggleBtn = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (toggleBtn && sidebar && mainContent) {
        // 从localStorage读取侧边栏状态
        const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (isCollapsed) {
            toggleSidebar(true);
        }

        toggleBtn.addEventListener('click', function() {
            toggleSidebar();
        });

        // 设置按钮提示
        toggleBtn.title = '收起/展开菜单 (Ctrl+B)';
    }
}

/**
 * 切换侧边栏显示/隐藏
 */
function toggleSidebar(forceCollapse = null) {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const toggleBtn = document.getElementById('sidebarToggle');

    if (!sidebar || !mainContent || !toggleBtn) return;

    const isCurrentlyCollapsed = sidebar.classList.contains('collapsed');
    const shouldCollapse = forceCollapse !== null ? forceCollapse : !isCurrentlyCollapsed;

    if (shouldCollapse) {
        // 收起侧边栏
        sidebar.classList.add('collapsed');
        document.body.classList.add('sidebar-collapsed');
        toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        toggleBtn.title = '展开菜单';

        // 保存状态到localStorage
        localStorage.setItem('sidebarCollapsed', 'true');
    } else {
        // 展开侧边栏
        sidebar.classList.remove('collapsed');
        document.body.classList.remove('sidebar-collapsed');
        toggleBtn.innerHTML = '<i class="fas fa-times"></i>';
        toggleBtn.title = '收起菜单';

        // 保存状态到localStorage
        localStorage.setItem('sidebarCollapsed', 'false');
    }
}

/**
 * 初始化键盘快捷键
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + B 或 Cmd + B 切换侧边栏
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }
    });
}

/**
 * 初始化用户信息
 */
function initializeUserInfo() {
    // 可以在这里加载用户信息
    console.log('用户信息初始化完成');
}

/**
 * 高亮当前导航
 */
function highlightCurrentNav() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
            
            // 展开父级折叠菜单
            const parentCollapse = link.closest('.collapse');
            if (parentCollapse) {
                parentCollapse.classList.add('show');
            }
        }
    });
}

/**
 * 登出功能
 */
async function logout() {
    try {
        const result = await request.post('/auth/logout');
        showMessage('登出成功', 'success');
        setTimeout(() => {
            window.location.href = '/auth/login';
        }, 1000);
    } catch (error) {
        showMessage('登出失败: ' + error.message, 'error');
    }
}

/**
 * 显示"敬请期待"消息
 */
function showComingSoon() {
    showMessage('该功能正在开发中，敬请期待！', 'info');
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

/**
 * 格式化数字
 */
function formatNumber(number, decimals = 2) {
    if (number === null || number === undefined) return '-';
    return Number(number).toFixed(decimals);
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 显示加载状态
 */
function showLoading(element, text = '加载中...') {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = `
            <div class="text-center py-4">
                <div class="loading-spinner mb-2"></div>
                <div class="text-muted">${text}</div>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = '';
    }
}

/**
 * 确认对话框
 */
function confirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * 复制到剪贴板
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showMessage('已复制到剪贴板', 'success');
    } catch (error) {
        showMessage('复制失败', 'error');
    }
}

/**
 * 下载文件
 */
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 获取URL参数
 */
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * 设置URL参数
 */
function setUrlParameter(name, value) {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.pushState({}, '', url);
}
