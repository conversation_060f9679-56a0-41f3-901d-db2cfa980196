from models import db, VehicleModel, SoundInsulationAreaModel, SoundInsulationDataModel, VehicleSoundInsulationModel, VehicleReverberationModel

class SoundInsulationService:
    """吸隔声业务逻辑服务"""
    
    def get_area_list(self):
        """获取所有测试区域列表"""
        areas = SoundInsulationAreaModel.get_all_areas()
        return [{'id': area.id, 'name': area.area_name, 'description': area.description} for area in areas]
    
    def get_vehicle_list(self, area_id=None):
        """获取车型列表"""
        if area_id:
            # 获取指定区域有数据的车型
            vehicles = SoundInsulationDataModel.get_vehicles_with_data_by_area(area_id)
        else:
            # 获取所有活跃车型
            vehicles = VehicleModel.query.filter_by(status='active').all()
        
        return [{'id': v.id, 'name': v.vehicle_model_name, 'code': v.vehicle_model_code} for v in vehicles]
    
    def generate_comparison_data(self, area_id, vehicle_ids):
        """生成区域隔声量对比数据"""
        try:
            # 获取区域信息
            area = SoundInsulationAreaModel.get_area_by_id(area_id)
            if not area:
                raise ValueError(f"区域ID {area_id} 不存在")
            
            # 获取车型信息
            vehicles = VehicleModel.query.filter(VehicleModel.id.in_(vehicle_ids)).all()
            if not vehicles:
                raise ValueError("未找到指定的车型")
            
            # 获取隔声量数据
            sound_data = SoundInsulationDataModel.get_data_by_area_and_vehicles(area_id, vehicle_ids)
            
            # 构建数据字典，以车型ID为键
            data_dict = {}
            test_info_dict = {}
            
            for data in sound_data:
                vehicle_id = data.vehicle_model_id
                data_dict[vehicle_id] = data.get_frequency_data()
                test_info_dict[vehicle_id] = {
                    'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else '',
                    'test_engineer': data.test_engineer or '',
                    'test_location': data.test_location or '',
                    'test_image_path': data.test_image_path or '',
                    'remarks': data.remarks or ''
                }
            
            # 构建表格数据
            frequency_labels = SoundInsulationDataModel.get_frequency_labels()
            table_data = []
            
            for freq in frequency_labels:
                row = {'frequency': freq}
                for vehicle in vehicles:
                    vehicle_key = f'vehicle_{vehicle.id}'
                    if vehicle.id in data_dict and data_dict[vehicle.id].get(freq) is not None:
                        row[vehicle_key] = data_dict[vehicle.id][freq]
                    else:
                        row[vehicle_key] = None
                table_data.append(row)
            
            # 构建图表数据
            chart_data = {
                'frequencies': frequency_labels,
                'series': []
            }
            
            for vehicle in vehicles:
                if vehicle.id in data_dict:
                    series_data = []
                    for freq in frequency_labels:
                        value = data_dict[vehicle.id].get(freq)
                        series_data.append(value)
                    
                    chart_data['series'].append({
                        'name': vehicle.vehicle_model_name,
                        'vehicle_id': vehicle.id,
                        'data': series_data
                    })
            
            # 构建车型信息
            vehicle_info = []
            for vehicle in vehicles:
                info = {
                    'id': vehicle.id,
                    'name': vehicle.vehicle_model_name,
                    'code': vehicle.vehicle_model_code
                }
                if vehicle.id in test_info_dict:
                    info.update(test_info_dict[vehicle.id])
                vehicle_info.append(info)
            
            return {
                'area_info': {
                    'id': area.id,
                    'name': area.area_name,
                    'description': area.description
                },
                'vehicle_info': vehicle_info,
                'table_data': table_data,
                'chart_data': chart_data,
                'frequency_labels': frequency_labels
            }
            
        except Exception as e:
            raise Exception(f"生成对比数据失败: {str(e)}")
    
    def get_test_image(self, vehicle_id, area_id):
        """获取测试图片信息"""
        try:
            data = SoundInsulationDataModel.query.filter_by(
                vehicle_model_id=vehicle_id,
                area_id=area_id
            ).first()
            
            if not data:
                return None
            
            vehicle = VehicleModel.query.get(vehicle_id)
            area = SoundInsulationAreaModel.get_area_by_id(area_id)
            
            return {
                'vehicle_name': vehicle.vehicle_model_name if vehicle else '',
                'area_name': area.area_name if area else '',
                'test_image_path': data.test_image_path or '',
                'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else '',
                'test_engineer': data.test_engineer or '',
                'test_location': data.test_location or '',
                'remarks': data.remarks or ''
            }
            
        except Exception as e:
            raise Exception(f"获取测试图片失败: {str(e)}")
    
    def export_comparison_data(self, area_id, vehicle_ids):
        """导出对比数据为CSV格式"""
        try:
            comparison_data = self.generate_comparison_data(area_id, vehicle_ids)
            
            # 构建CSV数据
            csv_data = []
            
            # 表头
            header = ['中心频率(Hz)']
            for vehicle_info in comparison_data['vehicle_info']:
                header.append(f"{vehicle_info['name']} (dB)")
            csv_data.append(header)
            
            # 数据行
            for row in comparison_data['table_data']:
                csv_row = [row['frequency']]
                for vehicle_info in comparison_data['vehicle_info']:
                    vehicle_key = f'vehicle_{vehicle_info["id"]}'
                    value = row.get(vehicle_key)
                    csv_row.append(str(value) if value is not None else '')
                csv_data.append(csv_row)
            
            return csv_data
            
        except Exception as e:
            raise Exception(f"导出数据失败: {str(e)}")

    # ========== 车型隔声量对比功能 ==========

    def get_vehicle_insulation_list(self):
        """获取有隔声量数据的车型列表"""
        vehicles = VehicleSoundInsulationModel.get_vehicles_with_data()
        return [{'id': v.id, 'name': v.vehicle_model_name, 'code': v.vehicle_model_code} for v in vehicles]

    def generate_vehicle_insulation_comparison(self, vehicle_ids):
        """生成车型隔声量对比数据"""
        try:
            # 获取车型信息
            vehicles = VehicleModel.query.filter(VehicleModel.id.in_(vehicle_ids)).all()
            if not vehicles:
                raise ValueError("未找到指定的车型")

            # 获取隔声量数据
            sound_data = VehicleSoundInsulationModel.get_comparison_data(vehicle_ids)

            # 构建数据字典，以车型ID为键
            data_dict = {}
            test_info_dict = {}

            for data in sound_data:
                vehicle_id = data.vehicle_model_id
                data_dict[vehicle_id] = data.get_frequency_data()
                test_info_dict[vehicle_id] = {
                    'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else '',
                    'test_engineer': data.test_engineer or '',
                    'test_location': data.test_location or '',
                    'test_image_path': data.test_image_path or '',
                    'remarks': data.remarks or ''
                }

            # 构建表格数据
            frequency_labels = VehicleSoundInsulationModel.get_frequency_labels()
            table_data = []

            for freq in frequency_labels:
                row = {'frequency': freq}
                for vehicle in vehicles:
                    vehicle_key = f'vehicle_{vehicle.id}'
                    if vehicle.id in data_dict and data_dict[vehicle.id].get(freq) is not None:
                        row[vehicle_key] = data_dict[vehicle.id][freq]
                    else:
                        row[vehicle_key] = None
                table_data.append(row)

            # 构建图表数据
            chart_data = {
                'frequencies': frequency_labels,
                'series': []
            }

            for vehicle in vehicles:
                if vehicle.id in data_dict:
                    series_data = []
                    for freq in frequency_labels:
                        value = data_dict[vehicle.id].get(freq)
                        series_data.append(value)

                    chart_data['series'].append({
                        'name': vehicle.vehicle_model_name,
                        'vehicle_id': vehicle.id,
                        'data': series_data
                    })

            # 构建车型信息
            vehicle_info = []
            for vehicle in vehicles:
                info = {
                    'id': vehicle.id,
                    'name': vehicle.vehicle_model_name,
                    'code': vehicle.vehicle_model_code
                }
                if vehicle.id in test_info_dict:
                    info.update(test_info_dict[vehicle.id])
                vehicle_info.append(info)

            return {
                'vehicle_info': vehicle_info,
                'table_data': table_data,
                'chart_data': chart_data,
                'frequency_labels': frequency_labels
            }

        except Exception as e:
            raise Exception(f"生成车型隔声量对比数据失败: {str(e)}")

    def get_vehicle_insulation_test_image(self, vehicle_id):
        """获取车型隔声量测试图片信息"""
        try:
            data = VehicleSoundInsulationModel.get_by_vehicle_id(vehicle_id)

            if not data:
                return None

            vehicle = VehicleModel.query.get(vehicle_id)

            return {
                'vehicle_name': vehicle.vehicle_model_name if vehicle else '',
                'test_image_path': data.test_image_path or '',
                'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else '',
                'test_engineer': data.test_engineer or '',
                'test_location': data.test_location or '',
                'remarks': data.remarks or ''
            }

        except Exception as e:
            raise Exception(f"获取车型隔声量测试图片失败: {str(e)}")

    def export_vehicle_insulation_data(self, vehicle_ids):
        """导出车型隔声量对比数据为CSV格式"""
        try:
            comparison_data = self.generate_vehicle_insulation_comparison(vehicle_ids)

            # 构建CSV数据
            csv_data = []

            # 表头
            header = ['中心频率(Hz)']
            for vehicle_info in comparison_data['vehicle_info']:
                header.append(f"{vehicle_info['name']} (dB)")
            csv_data.append(header)

            # 数据行
            for row in comparison_data['table_data']:
                csv_row = [row['frequency']]
                for vehicle_info in comparison_data['vehicle_info']:
                    vehicle_key = f'vehicle_{vehicle_info["id"]}'
                    value = row.get(vehicle_key)
                    csv_row.append(str(value) if value is not None else '')
                csv_data.append(csv_row)

            return csv_data

        except Exception as e:
            raise Exception(f"导出车型隔声量数据失败: {str(e)}")

    # ========== 车型混响时间对比功能 ==========

    def get_vehicle_reverberation_list(self):
        """获取有混响时间数据的车型列表"""
        vehicles = VehicleReverberationModel.get_vehicles_with_data()
        return [{'id': v.id, 'name': v.vehicle_model_name, 'code': v.vehicle_model_code} for v in vehicles]

    def generate_vehicle_reverberation_comparison(self, vehicle_ids):
        """生成车型混响时间对比数据"""
        try:
            # 获取车型信息
            vehicles = VehicleModel.query.filter(VehicleModel.id.in_(vehicle_ids)).all()
            if not vehicles:
                raise ValueError("未找到指定的车型")

            # 获取混响时间数据
            reverberation_data = VehicleReverberationModel.get_comparison_data(vehicle_ids)

            # 构建数据字典，以车型ID为键
            data_dict = {}
            test_info_dict = {}

            for data in reverberation_data:
                vehicle_id = data.vehicle_model_id
                data_dict[vehicle_id] = data.get_frequency_data()
                test_info_dict[vehicle_id] = {
                    'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else '',
                    'test_engineer': data.test_engineer or '',
                    'test_location': data.test_location or '',
                    'test_image_path': data.test_image_path or '',
                    'remarks': data.remarks or ''
                }

            # 构建表格数据
            frequency_labels = VehicleReverberationModel.get_frequency_labels()
            table_data = []

            for freq in frequency_labels:
                row = {'frequency': freq}
                for vehicle in vehicles:
                    vehicle_key = f'vehicle_{vehicle.id}'
                    if vehicle.id in data_dict and data_dict[vehicle.id].get(freq) is not None:
                        row[vehicle_key] = data_dict[vehicle.id][freq]
                    else:
                        row[vehicle_key] = None
                table_data.append(row)

            # 构建图表数据
            chart_data = {
                'frequencies': frequency_labels,
                'series': []
            }

            for vehicle in vehicles:
                if vehicle.id in data_dict:
                    series_data = []
                    for freq in frequency_labels:
                        value = data_dict[vehicle.id].get(freq)
                        series_data.append(value)

                    chart_data['series'].append({
                        'name': vehicle.vehicle_model_name,
                        'vehicle_id': vehicle.id,
                        'data': series_data
                    })

            # 构建车型信息
            vehicle_info = []
            for vehicle in vehicles:
                info = {
                    'id': vehicle.id,
                    'name': vehicle.vehicle_model_name,
                    'code': vehicle.vehicle_model_code
                }
                if vehicle.id in test_info_dict:
                    info.update(test_info_dict[vehicle.id])
                vehicle_info.append(info)

            return {
                'vehicle_info': vehicle_info,
                'table_data': table_data,
                'chart_data': chart_data,
                'frequency_labels': frequency_labels
            }

        except Exception as e:
            raise Exception(f"生成车型混响时间对比数据失败: {str(e)}")

    def get_vehicle_reverberation_test_image(self, vehicle_id):
        """获取车型混响时间测试图片信息"""
        try:
            data = VehicleReverberationModel.get_by_vehicle_id(vehicle_id)

            if not data:
                return None

            vehicle = VehicleModel.query.get(vehicle_id)

            return {
                'vehicle_name': vehicle.vehicle_model_name if vehicle else '',
                'test_image_path': data.test_image_path or '',
                'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else '',
                'test_engineer': data.test_engineer or '',
                'test_location': data.test_location or '',
                'remarks': data.remarks or ''
            }

        except Exception as e:
            raise Exception(f"获取车型混响时间测试图片失败: {str(e)}")

    def export_vehicle_reverberation_data(self, vehicle_ids):
        """导出车型混响时间对比数据为CSV格式"""
        try:
            comparison_data = self.generate_vehicle_reverberation_comparison(vehicle_ids)

            # 构建CSV数据
            csv_data = []

            # 表头
            header = ['中心频率(Hz)']
            for vehicle_info in comparison_data['vehicle_info']:
                header.append(f"{vehicle_info['name']} (s)")
            csv_data.append(header)

            # 数据行
            for row in comparison_data['table_data']:
                csv_row = [row['frequency']]
                for vehicle_info in comparison_data['vehicle_info']:
                    vehicle_key = f'vehicle_{vehicle_info["id"]}'
                    value = row.get(vehicle_key)
                    csv_row.append(str(value) if value is not None else '')
                csv_data.append(csv_row)

            return csv_data

        except Exception as e:
            raise Exception(f"导出车型混响时间数据失败: {str(e)}")
