from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel, 
    MaterialModel, 
    MaterialManufacturerModel, 
    WallMountedTransmissionLossModel
)
import csv
import io

class WallMountedTransmissionService:
    """上墙法隔声量业务逻辑服务"""
    
    def get_part_list(self):
        """获取零件列表"""
        parts = SoundInsulationPartModel.get_parts_with_wall_mounted_transmission_data()
        return [{'name': part.part_name, 'description': part.description} for part in parts]
    
    def get_material_list(self, part_name=None):
        """获取材料列表"""
        if part_name:
            materials = MaterialModel.get_materials_by_part_name_for_wall_mounted_transmission(part_name)
        else:
            materials = MaterialModel.get_all_materials()

        # 按材料名称分组，每个材料名称只返回一次
        material_dict = {}
        for material in materials:
            if material.material_name not in material_dict:
                material_dict[material.material_name] = {
                    'name': material.material_name,
                    'description': material.description
                }

        return list(material_dict.values())
    
    def get_weight_list(self, part_name, material_name):
        """获取克重列表"""
        if not part_name or not material_name:
            return []
        
        weights = WallMountedTransmissionLossModel.get_weights_by_part_material(part_name, material_name)
        return [{'weight': weight} for weight in weights]
    
    def get_transmission_data(self, part_name, material_name, weight):
        """获取上墙法隔声量数据"""
        if not all([part_name, material_name, weight]):
            return None
        
        # 查询数据
        data = WallMountedTransmissionLossModel.get_data_by_conditions(part_name, material_name, weight)
        if not data:
            return None
        
        # 转换为字典格式
        result = data.to_dict_with_frequency_data()
        
        # 格式化日期
        if result.get('test_date'):
            result['test_date'] = result['test_date'].strftime('%Y-%m-%d')
        
        return result
    
    def export_data_to_csv(self, part_name, material_name, weight):
        """导出单个数据为CSV"""
        data = self.get_transmission_data(part_name, material_name, weight)
        if not data:
            return None
        
        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入标题信息
        writer.writerow(['上墙法隔声量数据'])
        writer.writerow(['零件名称', data.get('part_name', '')])
        writer.writerow(['材料名称', data.get('material_name', '')])
        writer.writerow(['厚度(mm)', data.get('thickness', '')])
        writer.writerow(['克重(g/m²)', data.get('weight', '')])
        writer.writerow(['测试机构', data.get('test_institution', '')])
        writer.writerow(['测试日期', data.get('test_date', '')])
        writer.writerow(['测试地点', data.get('test_location', '')])
        writer.writerow(['测试工程师', data.get('test_engineer', '')])
        writer.writerow([])  # 空行
        
        # 写入频率数据表头
        freq_labels = WallMountedTransmissionLossModel.get_frequency_labels()
        writer.writerow(['数据类型'] + freq_labels)
        
        # 写入测试值
        test_data = data.get('test_frequency_data', {})
        test_values = [test_data.get(freq, '') for freq in freq_labels]
        writer.writerow(['测试值(dB)'] + test_values)
        
        # 写入目标值
        target_data = data.get('target_frequency_data', {})
        target_values = [target_data.get(freq, '') for freq in freq_labels]
        writer.writerow(['目标值(dB)'] + target_values)
        
        return output.getvalue()
    
    def get_manufacturer_list(self):
        """获取厂家列表"""
        manufacturers = MaterialManufacturerModel.get_all_manufacturers()
        return [{'name': mfr.manufacturer_name, 'description': mfr.description} for mfr in manufacturers]
    
    def get_test_image_info(self, part_name, material_name, weight):
        """获取测试图片信息"""
        data = WallMountedTransmissionLossModel.get_data_by_conditions(part_name, material_name, weight)
        if not data:
            return None
        
        return {
            'image_path': data.test_image_path,
            'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else None,
            'test_location': data.test_location,
            'test_engineer': data.test_engineer,
            'test_institution': data.test_institution,
            'remarks': data.remarks,
            'part_name': data.part_name,
            'material_name': data.material_name,
            'weight': float(data.weight) if data.weight else None,
            'thickness': float(data.thickness) if data.thickness else None,
            'manufacturer_name': data.manufacturer_name
        }
    
    def search_data(self, filters=None):
        """搜索数据"""
        query = WallMountedTransmissionLossModel.query
        
        if filters:
            if filters.get('part_name'):
                query = query.join(SoundInsulationPartModel).filter(
                    SoundInsulationPartModel.part_name.like(f"%{filters['part_name']}%")
                )
            
            if filters.get('material_name'):
                query = query.join(MaterialModel).filter(
                    MaterialModel.material_name.like(f"%{filters['material_name']}%")
                )
            
            if filters.get('weight_min'):
                query = query.join(MaterialModel).filter(
                    MaterialModel.weight >= filters['weight_min']
                )
            
            if filters.get('weight_max'):
                query = query.join(MaterialModel).filter(
                    MaterialModel.weight <= filters['weight_max']
                )
        
        # 执行查询
        results = query.all()
        
        # 转换为字典列表
        data_list = []
        for item in results:
            item_dict = item.to_dict_with_frequency_data()
            if item_dict.get('test_date'):
                item_dict['test_date'] = item_dict['test_date'].strftime('%Y-%m-%d')
            data_list.append(item_dict)
        
        return data_list
    
    def get_statistics(self):
        """获取统计信息"""
        stats = {
            'total_records': WallMountedTransmissionLossModel.query.count(),
            'total_parts': len(SoundInsulationPartModel.get_parts_with_wall_mounted_transmission_data()),
            'total_materials': db.session.query(MaterialModel).join(
                WallMountedTransmissionLossModel, MaterialModel.id == WallMountedTransmissionLossModel.material_id
            ).distinct().count(),
            'total_manufacturers': db.session.query(MaterialManufacturerModel).join(
                WallMountedTransmissionLossModel, MaterialManufacturerModel.id == WallMountedTransmissionLossModel.manufacturer_id
            ).distinct().count()
        }
        return stats

# 创建服务实例
wall_mounted_transmission_service = WallMountedTransmissionService()
