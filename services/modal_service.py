from models import db
from models.modal_data_model import ModalDataModel
from models.vehicle_model import VehicleModel
from models.component_model import ComponentModel
from models.test_project_model import TestProjectModel

class ModalService:
    """模态数据业务逻辑"""
    
    def search_modal_data(self, params):
        """搜索模态数据"""
        vehicle_id = params.get('vehicle_model_id')
        component_ids = params.get('component_ids', [])

        query = db.session.query(
            ModalDataModel,
            TestProjectModel,
            VehicleModel,
            ComponentModel
        ).join(
            TestProjectModel, ModalDataModel.test_project_id == TestProjectModel.id
        ).join(
            VehicleModel, TestProjectModel.vehicle_model_id == VehicleModel.id
        ).outerjoin(
            ComponentModel, TestProjectModel.component_id == ComponentModel.id
        )

        # 筛选条件
        if vehicle_id:
            query = query.filter(VehicleModel.id == vehicle_id)
        if component_ids and len(component_ids) > 0:
            # 支持多个零件ID的筛选
            query = query.filter(ComponentModel.id.in_(component_ids))
        
        results = query.all()
        
        # 数据转换
        data_list = []
        for modal, project, vehicle, component in results:
            item = {
                'id': modal.id,
                'category': component.category if component else '整车',
                'sub_category': component.sub_category if component else '',
                'component_name': component.component_name if component else '整车',
                'frequency': float(modal.frequency),
                'mode_order': modal.mode_order,
                'mode_shape_description': modal.mode_shape_description,
                'direction': modal.direction,
                'damping_ratio': float(modal.damping_ratio) if modal.damping_ratio else None,
                'vehicle_name': vehicle.vehicle_model_name,
                'test_date': project.test_date.strftime('%Y-%m-%d'),
                'test_engineer': project.test_engineer,
                'test_condition': project.test_condition
            }
            data_list.append(item)
        
        return data_list
    
    def get_modal_detail(self, data_id):
        """获取模态数据详情"""
        modal_data = ModalDataModel.query.get(data_id)
        if not modal_data:
            return None
        
        # 关联查询
        project = TestProjectModel.query.get(modal_data.test_project_id)
        vehicle = VehicleModel.query.get(project.vehicle_model_id)
        component = ComponentModel.query.get(project.component_id) if project.component_id else None
        
        detail = modal_data.to_dict()
        detail.update({
            'project_info': project.to_dict(),
            'vehicle_info': vehicle.to_dict(),
            'component_info': component.to_dict() if component else None
        })
        
        return detail
    
    def get_vehicle_list(self):
        """获取车型列表"""
        vehicles = VehicleModel.query.filter_by(status='active').all()
        return [{'id': v.id, 'name': v.vehicle_model_name, 'code': v.vehicle_model_code} for v in vehicles]
    
    def get_component_list(self, vehicle_id=None):
        """获取零部件列表"""
        query = ComponentModel.query
        if vehicle_id:
            # 根据车型筛选零部件（通过测试项目关联）
            query = query.join(TestProjectModel).filter(TestProjectModel.vehicle_model_id == vehicle_id)

        components = query.distinct().all()
        return [{'id': c.id, 'name': c.component_name, 'category': c.category} for c in components]

    def get_test_conditions_list(self):
        """获取测试状态列表"""
        conditions = db.session.query(TestProjectModel.test_condition)\
            .filter(TestProjectModel.test_condition.isnot(None))\
            .distinct().all()
        return [{'value': condition[0], 'label': condition[0]} for condition in conditions if condition[0]]

    def get_mode_shapes_list(self):
        """获取模态振型列表"""
        mode_shapes = db.session.query(ModalDataModel.mode_shape_description)\
            .filter(ModalDataModel.mode_shape_description.isnot(None))\
            .distinct().all()
        return [{'value': shape[0], 'label': shape[0]} for shape in mode_shapes if shape[0]]

    def get_component_options(self, component_id):
        """根据零件ID获取对应的测试状态和模态振型选项"""
        # 获取该零件对应的测试状态
        test_conditions = db.session.query(TestProjectModel.test_condition)\
            .filter(TestProjectModel.component_id == component_id)\
            .filter(TestProjectModel.test_condition.isnot(None))\
            .distinct().all()

        # 获取该零件对应的模态振型
        mode_shapes = db.session.query(ModalDataModel.mode_shape_description)\
            .join(TestProjectModel, ModalDataModel.test_project_id == TestProjectModel.id)\
            .filter(TestProjectModel.component_id == component_id)\
            .filter(ModalDataModel.mode_shape_description.isnot(None))\
            .distinct().all()

        return {
            'test_conditions': [{'value': condition[0], 'label': condition[0]} for condition in test_conditions if condition[0]],
            'mode_shapes': [{'value': shape[0], 'label': shape[0]} for shape in mode_shapes if shape[0]]
        }

    def get_modal_comparison_data(self, params):
        """获取模态对标数据"""
        component_ids = params.get('component_ids', [])
        vehicle_ids = params.get('vehicle_ids', [])
        test_conditions = params.get('test_conditions', [])
        mode_shapes = params.get('mode_shapes', [])

        # 构建查询
        query = db.session.query(
            ModalDataModel,
            TestProjectModel,
            VehicleModel,
            ComponentModel
        ).join(
            TestProjectModel, ModalDataModel.test_project_id == TestProjectModel.id
        ).join(
            VehicleModel, TestProjectModel.vehicle_model_id == VehicleModel.id
        ).outerjoin(
            ComponentModel, TestProjectModel.component_id == ComponentModel.id
        )

        # 应用筛选条件
        if component_ids:
            query = query.filter(ComponentModel.id.in_(component_ids))
        if vehicle_ids:
            query = query.filter(VehicleModel.id.in_(vehicle_ids))
        if test_conditions:
            query = query.filter(TestProjectModel.test_condition.in_(test_conditions))
        if mode_shapes:
            query = query.filter(ModalDataModel.mode_shape_description.in_(mode_shapes))

        results = query.all()

        # 判断标签显示格式
        # 多车型 + 单状态：只显示车型名称
        # 单车型 + 多状态：显示车型_测试状态
        # 其他情况：保持原格式车型_测试状态
        use_vehicle_only = len(vehicle_ids) > 1 and len(test_conditions) == 1
        label_type = 'vehicle_only' if use_vehicle_only else 'vehicle_condition'

        # 数据处理
        comparison_data = {}
        chart_data = []

        for modal, project, vehicle, component in results:
            # 根据业务逻辑生成标识
            if use_vehicle_only:
                # 多车型单状态：只显示车型名称
                vehicle_condition = vehicle.vehicle_model_name
            else:
                # 其他情况：显示车型_测试状态
                vehicle_condition = f"{vehicle.vehicle_model_name}_{project.test_condition}"

            mode_shape = modal.mode_shape_description
            frequency = float(modal.frequency)

            # 构建对比表格数据
            if mode_shape not in comparison_data:
                comparison_data[mode_shape] = {}
            comparison_data[mode_shape][vehicle_condition] = frequency

            # 构建图表数据
            chart_data.append({
                'vehicle_condition': vehicle_condition,
                'mode_shape': mode_shape,
                'frequency': frequency,
                'vehicle_name': vehicle.vehicle_model_name,
                'test_condition': project.test_condition,
                'component_name': component.component_name if component else '整车'
            })

        # 生成表格结构
        all_vehicle_conditions = set()
        for mode_data in comparison_data.values():
            all_vehicle_conditions.update(mode_data.keys())

        headers = ['振型类型'] + sorted(list(all_vehicle_conditions))
        rows = []

        for mode_shape, vehicle_data in comparison_data.items():
            row = {'mode_shape': mode_shape}
            for vehicle_condition in sorted(list(all_vehicle_conditions)):
                row[vehicle_condition] = vehicle_data.get(vehicle_condition, '-')
            rows.append(row)

        return {
            'comparison_table': {
                'headers': headers,
                'rows': rows
            },
            'chart_data': chart_data,
            'label_type': label_type  # 新增：标签类型信息
        }

    def find_modal_data_by_conditions(self, params):
        """根据条件查找模态数据"""
        vehicle_condition = params.get('vehicle_condition', '')
        mode_shape = params.get('mode_shape', '')
        frequency = float(params.get('frequency', 0))
        component_name = params.get('component_name', '')

        # 解析车型_状态
        if '_' in vehicle_condition:
            vehicle_name, test_condition = vehicle_condition.rsplit('_', 1)
        else:
            vehicle_name = vehicle_condition
            test_condition = None

        # 构建查询
        query = db.session.query(ModalDataModel).join(
            TestProjectModel, ModalDataModel.test_project_id == TestProjectModel.id
        ).join(
            VehicleModel, TestProjectModel.vehicle_model_id == VehicleModel.id
        ).outerjoin(
            ComponentModel, TestProjectModel.component_id == ComponentModel.id
        ).filter(
            VehicleModel.vehicle_model_name == vehicle_name,
            ModalDataModel.mode_shape_description == mode_shape,
            ModalDataModel.frequency == frequency
        )

        # 添加测试条件筛选
        if test_condition:
            query = query.filter(TestProjectModel.test_condition == test_condition)

        # 添加零件名称筛选
        if component_name and component_name != '整车':
            query = query.filter(ComponentModel.component_name == component_name)
        elif component_name == '整车':
            query = query.filter(TestProjectModel.component_id.is_(None))

        modal_data = query.first()

        if modal_data:
            return {'id': modal_data.id}
        else:
            return None
