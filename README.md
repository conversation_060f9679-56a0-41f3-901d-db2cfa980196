# NVH数据管理系统

基于Flask + SQLAlchemy + MySQL + Keycloak的NVH数据管理系统，提供模态数据查询、分析和管理功能。

## 功能特性

- **用户认证**: 集成Keycloak统一认证
- **模态数据管理**: 车型和零部件模态数据查询、展示
- **数据可视化**: 模态振型GIF展示、测试照片查看
- **数据导出**: 支持CSV格式数据导出
- **响应式设计**: 支持PC和移动端访问

## 技术栈

- **后端**: Flask 2.3.3 + SQLAlchemy + PyMySQL
- **前端**: Bootstrap 5 + JavaScript + Jinja2
- **数据库**: MySQL 8.0+
- **认证**: Keycloak OAuth2/OIDC
- **部署**: 支持Docker部署

## 项目结构

```
nvh_system/
├── app.py                    # 应用入口
├── config.py                 # 配置文件
├── decorators.py            # 装饰器（认证等）
├── requirements.txt         # 依赖包
├── models/                  # 数据模型
│   ├── base_model.py        # 基础模型类
│   ├── vehicle_model.py     # 车型模型
│   ├── component_model.py   # 零部件模型
│   ├── test_project_model.py # 测试项目模型
│   └── modal_data_model.py  # 模态数据模型
├── controllers/             # 控制器
│   ├── auth_controller.py   # 认证控制器
│   └── modal_controller.py  # 模态数据控制器
├── services/               # 业务逻辑层
│   └── modal_service.py    # 模态数据业务逻辑
├── utils/                  # 工具类
│   └── result.py          # 统一返回结果
├── static/                # 静态文件
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript文件
│   └── uploads/           # 上传文件存储
└── templates/             # 模板文件
    ├── base.html          # 基础模板
    ├── index.html         # 首页
    ├── login.html         # 登录页面
    └── modal/             # 模态数据相关模板
```

## 快速开始

### 1. 环境准备

- Python 3.8+
- MySQL 8.0+
- Keycloak服务器

### 2. 安装依赖

```bash

```

### 3. 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE nvh_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行数据库初始化脚本（见已有数据库表.md）

### 4. 环境变量配置

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接信息。

### 5. 启动应用

```bash
python app.py
```

应用将在 http://localhost:5000 启动。

## 使用说明

### 访问系统

1. 访问 http://localhost:5000
2. 系统自动重定向到Keycloak认证页面
3. 完成认证后自动跳转回系统主页

### 模态数据查询

1. 认证成功后，点击左侧菜单"NVH数据查询" -> "模态数据"
2. 选择车型（必选）
3. 选择零部件（可选，不选则查询所有零部件）
4. 点击"搜索"按钮
5. 在结果表格中点击"查看详情"可查看详细信息

### 数据导出

在搜索结果页面点击"导出数据"按钮，可将当前搜索结果导出为CSV文件。

## API接口

### 认证相关

- `GET /auth/callback` - OAuth认证回调
- `POST /auth/logout` - 登出
- `GET /auth/user/info` - 获取用户信息

### 模态数据相关

- `GET /modal/search` - 搜索页面
- `GET /modal/api/search` - 搜索模态数据
- `GET /modal/api/data/<id>` - 获取模态数据详情
- `GET /modal/api/vehicles` - 获取车型列表
- `GET /modal/api/components` - 获取零部件列表

## 开发说明

### 添加新功能模块

1. 在 `models/` 目录下创建数据模型
2. 在 `services/` 目录下创建业务逻辑
3. 在 `controllers/` 目录下创建控制器
4. 在 `templates/` 目录下创建模板
5. 在 `static/` 目录下创建样式和脚本

### 代码规范

- Python: PEP 8
- JavaScript: ES6+
- CSS: BEM命名规范
- 文件命名: 小写+下划线

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t nvh-system .

# 运行容器
docker run -d -p 5000:5000 --name nvh-system nvh-system
```

### 生产环境配置

1. 设置环境变量 `FLASK_ENV=production`
2. 配置反向代理（Nginx）
3. 使用WSGI服务器（Gunicorn）
4. 配置SSL证书

## 许可证

MIT License
