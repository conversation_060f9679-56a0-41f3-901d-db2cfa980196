{% extends "base_tabs.html" %}

{% block title %}标签页测试 - NVH数据管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>标签页功能测试</h2>
            <p>这是一个测试页面，用于验证多标签页导航系统的功能。</p>
            
            <div class="alert alert-info">
                <h5>测试说明：</h5>
                <ul>
                    <li>点击左侧边栏的菜单项应该会创建新的标签页</li>
                    <li>标签页应该显示在顶部导航栏下方</li>
                    <li>每个标签页都有关闭按钮（除了首页）</li>
                    <li>点击标签页可以切换内容</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>当前标签页信息</h5>
                </div>
                <div class="card-body">
                    <p><strong>标签页ID：</strong><span id="current-tab-id">home</span></p>
                    <p><strong>活跃标签页数量：</strong><span id="tab-count">1</span></p>
                    <button class="btn btn-primary" onclick="updateTabInfo()">刷新信息</button>
                </div>
            </div>
            
            <div class="mt-4">
                <h5>测试链接</h5>
                <div class="btn-group" role="group">
                    <a href="/airtightness/comparison" class="btn btn-outline-primary">气密性对比</a>
                    <a href="/airtightness/images" class="btn btn-outline-secondary">气密性图片</a>
                    <a href="/modal/search" class="btn btn-outline-success">模态数据</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateTabInfo() {
    if (typeof tabManager !== 'undefined' && tabManager) {
        document.getElementById('current-tab-id').textContent = tabManager.activeTabId;
        document.getElementById('tab-count').textContent = tabManager.tabs.size;
    } else {
        document.getElementById('current-tab-id').textContent = '标签页管理器未初始化';
        document.getElementById('tab-count').textContent = '0';
    }
}

// 页面加载后更新信息
setTimeout(updateTabInfo, 1000);
</script>
{% endblock %}
