<!-- 垂直入射法吸音系数查询页面内容片段 -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">垂直入射法吸音系数查询</h1>
</div>

<!-- 查询条件 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="query-form">
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="part-select" class="form-label">零件</label>
                    <select class="form-select" id="part-select" required>
                        <option value="">请选择零件</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="material-select" class="form-label">材料</label>
                    <select class="form-select" id="material-select" required disabled>
                        <option value="">请先选择零件</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="weight-select" class="form-label">克重</label>
                    <select class="form-select" id="weight-select" required disabled>
                        <option value="">请先选择材料</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary" id="search-btn" disabled>
                        <i class="fas fa-search me-1"></i>查询
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" id="multi-compare-btn" disabled>
                        <i class="fas fa-chart-line me-1"></i>多克重对比
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>查询结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <button type="button" class="btn btn-outline-success btn-sm" id="export-csv-btn">
                <i class="fas fa-download me-1"></i>导出CSV
            </button>
            <button type="button" class="btn btn-outline-info btn-sm" id="view-image-btn">
                <i class="fas fa-image me-1"></i>查看测试图片
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- 基本信息 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle me-2"></i>基本信息</h6>
                <table class="table table-sm table-bordered">
                    <tr>
                        <td><strong>零件名称</strong></td>
                        <td id="result-part-name">-</td>
                    </tr>
                    <tr>
                        <td><strong>材料名称</strong></td>
                        <td id="result-material-name">-</td>
                    </tr>
                    <tr>
                        <td><strong>克重</strong></td>
                        <td id="result-weight">-</td>
                    </tr>
                    <tr>
                        <td><strong>厚度</strong></td>
                        <td id="result-thickness">-</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-user me-2"></i>测试信息</h6>
                <table class="table table-sm table-bordered">
                    <tr>
                        <td><strong>测试日期</strong></td>
                        <td id="result-test-date">-</td>
                    </tr>
                    <tr>
                        <td><strong>测试工程师</strong></td>
                        <td id="result-test-engineer">-</td>
                    </tr>
                    <tr>
                        <td><strong>测试地点</strong></td>
                        <td id="result-test-location">-</td>
                    </tr>
                    <tr>
                        <td><strong>备注</strong></td>
                        <td id="result-notes">-</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 吸音系数数据表格 -->
        <div class="mb-4">
            <h6><i class="fas fa-table me-2"></i>吸音系数数据</h6>
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="data-table">
                    <thead class="table-dark">
                        <tr>
                            <th>频率 (Hz)</th>
                            <th>吸音系数</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态生成数据 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 图表展示 -->
        <div class="mb-4">
            <h6><i class="fas fa-chart-line me-2"></i>吸音系数曲线</h6>
            <div id="chart-container" style="width: 100%; height: 400px;"></div>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择查询条件</h5>
        <p class="text-muted">选择零件、材料和克重，点击"查询"按钮查看吸音系数数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在查询数据...</p>
</div>

<!-- 测试图片模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">测试图片</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div id="image-container">
                    <img id="test-image" src="" alt="测试图片" class="img-fluid" style="max-width: 100%; height: auto;">
                </div>
                <div id="image-info" class="mt-3">
                    <!-- 图片信息 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="download-image-btn">下载原图</button>
            </div>
        </div>
    </div>
</div>

<!-- 引入必要的CSS和JS -->
<link href="/static/css/sound_absorption.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
// 确保在标签页环境中正确加载脚本
if (typeof SoundAbsorptionQuery === 'undefined') {
    // 动态加载 sound_absorption.js
    const script = document.createElement('script');
    script.src = '/static/js/sound_absorption.js';
    script.onload = function() {
        // 脚本加载完成后初始化
        if (typeof window.initializeSoundAbsorptionQuery === 'function') {
            window.initializeSoundAbsorptionQuery();
        }
    };
    document.head.appendChild(script);
} else {
    // 脚本已加载，直接初始化
    setTimeout(() => {
        if (typeof window.initializeSoundAbsorptionQuery === 'function') {
            window.initializeSoundAbsorptionQuery();
        }
    }, 100);
}
</script>
