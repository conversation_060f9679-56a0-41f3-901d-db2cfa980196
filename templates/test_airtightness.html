{% extends "base.html" %}

{% block title %}气密性功能测试 - NVH数据管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>气密性功能测试</h2>
            <p>这是一个独立的测试页面，用于验证气密性功能是否正常工作。</p>
            
            <div class="alert alert-info">
                <h5>测试步骤：</h5>
                <ol>
                    <li>点击"测试车型数据加载"按钮</li>
                    <li>检查控制台输出</li>
                    <li>查看车型选择框是否有数据</li>
                </ol>
            </div>
            
            <!-- 车型选择区域 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3 align-items-start">
                        <div class="col-md-6">
                            <div class="vehicle-multiselect" id="vehicle-multiselect">
                                <div class="multiselect-container">
                                    <div class="multiselect-input-container">
                                        <input type="text" class="form-control multiselect-input" placeholder="点击选择车型..." readonly>
                                        <i class="fas fa-chevron-down multiselect-arrow"></i>
                                    </div>
                                    <div class="multiselect-dropdown">
                                        <div class="multiselect-search">
                                            <input type="text" class="form-control form-control-sm" placeholder="搜索车型...">
                                        </div>
                                        <div class="multiselect-options">
                                            <!-- 动态加载选项 -->
                                        </div>
                                    </div>
                                </div>
                                <div class="selected-items mt-2">
                                    <!-- 已选择的车型标签 -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex align-items-start">
                            <button type="button" class="btn btn-primary" id="generate-comparison-btn" disabled>
                                <i class="fas fa-chart-bar me-1"></i>生成对比表
                            </button>
                            <button type="button" class="btn btn-success ms-2" onclick="testVehicleLoading()">
                                <i class="fas fa-test me-1"></i>测试车型数据加载
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>测试结果</h5>
                </div>
                <div class="card-body">
                    <div id="test-results">
                        <p>点击测试按钮开始测试...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入必要的CSS和JS -->
<link href="/static/css/airtightness.css" rel="stylesheet">
<script src="/static/js/airtightness.js"></script>

<script>
let testManager = null;

function testVehicleLoading() {
    const resultsDiv = document.getElementById('test-results');
    resultsDiv.innerHTML = '<p>开始测试...</p>';
    
    console.log('开始测试车型数据加载');
    
    try {
        // 测试1: 检查类是否定义
        if (typeof AirtightnessComparisonManager === 'undefined') {
            resultsDiv.innerHTML += '<p class="text-danger">❌ AirtightnessComparisonManager 类未定义</p>';
            return;
        } else {
            resultsDiv.innerHTML += '<p class="text-success">✅ AirtightnessComparisonManager 类已定义</p>';
        }
        
        // 测试2: 检查DOM元素
        const vehicleElement = document.getElementById('vehicle-multiselect');
        const generateBtn = document.getElementById('generate-comparison-btn');
        
        if (!vehicleElement) {
            resultsDiv.innerHTML += '<p class="text-danger">❌ vehicle-multiselect 元素未找到</p>';
            return;
        } else {
            resultsDiv.innerHTML += '<p class="text-success">✅ vehicle-multiselect 元素已找到</p>';
        }
        
        if (!generateBtn) {
            resultsDiv.innerHTML += '<p class="text-danger">❌ generate-comparison-btn 元素未找到</p>';
            return;
        } else {
            resultsDiv.innerHTML += '<p class="text-success">✅ generate-comparison-btn 元素已找到</p>';
        }
        
        // 测试3: 创建管理器实例
        try {
            testManager = new AirtightnessComparisonManager();
            resultsDiv.innerHTML += '<p class="text-success">✅ AirtightnessComparisonManager 实例创建成功</p>';
        } catch (error) {
            resultsDiv.innerHTML += '<p class="text-danger">❌ 创建 AirtightnessComparisonManager 实例失败: ' + error.message + '</p>';
            return;
        }
        
        // 测试4: 检查车型数据API
        setTimeout(async () => {
            try {
                const response = await fetch('/airtightness/api/vehicles');
                const result = await response.json();
                
                if (result.code === 200) {
                    resultsDiv.innerHTML += '<p class="text-success">✅ 车型数据API调用成功，返回 ' + result.data.length + ' 个车型</p>';
                    resultsDiv.innerHTML += '<p class="text-info">车型列表: ' + result.data.map(v => v.name).join(', ') + '</p>';
                } else {
                    resultsDiv.innerHTML += '<p class="text-danger">❌ 车型数据API调用失败: ' + result.message + '</p>';
                }
            } catch (error) {
                resultsDiv.innerHTML += '<p class="text-danger">❌ 车型数据API调用异常: ' + error.message + '</p>';
            }
        }, 1000);
        
    } catch (error) {
        resultsDiv.innerHTML += '<p class="text-danger">❌ 测试过程中发生错误: ' + error.message + '</p>';
        console.error('测试错误:', error);
    }
}

// 页面加载完成后自动运行测试
setTimeout(() => {
    testVehicleLoading();
}, 1000);
</script>
{% endblock %}
