<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}NVH数据管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/base.css') }}" rel="stylesheet">
    {% block head %}{% endblock %}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            {% if session.user %}
            <button class="btn btn-outline-light me-3" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            {% endif %}
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>NVH数据管理系统
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ session.user.name or session.user.preferred_username or '用户' }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {% if session.user %}
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar" id="sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('index') }}">
                                <i class="fas fa-home me-2"></i>首页
                            </a>
                        </li>
                        
                        <!-- NVH数据查询 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#nvhDataCollapse">
                                <i class="fas fa-search me-2"></i>NVH数据查询
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="nvhDataCollapse">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('modal.search_page') }}">
                                            <i class="fas fa-wave-square me-2"></i>模态数据
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showComingSoon()">
                                            <i class="fas fa-volume-up me-2"></i>声品质
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showComingSoon()">
                                            <i class="fas fa-chart-area me-2"></i>振动
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- 气密性测试 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#airtightnessCollapse">
                                <i class="fas fa-tachometer-alt me-2"></i>气密性测试
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="airtightnessCollapse">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('airtightness.comparison_page') }}">
                                            <i class="fas fa-chart-bar me-2"></i>泄漏量对比
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('airtightness.images_page') }}">
                                            <i class="fas fa-images me-2"></i>测试图片
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- 吸隔声模块 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#soundInsulationCollapse">
                                <i class="fas fa-volume-up me-2"></i>吸隔声模块
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="soundInsulationCollapse">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('sound_insulation.area_comparison_page') }}">
                                            <i class="fas fa-chart-line me-2"></i>区域隔声量
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('sound_insulation.vehicle_insulation_page') }}">
                                            <i class="fas fa-car me-2"></i>车型隔声量对比
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('sound_insulation.vehicle_reverberation_page') }}">
                                            <i class="fas fa-wave-square me-2"></i>车型混响时间对比
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('sound_absorption.coefficient_query_page') }}">
                                            <i class="fas fa-search me-2"></i>垂直入射吸音系数查询
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('sound_transmission.transmission_loss_query_page') }}">
                                            <i class="fas fa-shield-alt me-2"></i>垂直入射法隔声量查询
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('wall_mounted_transmission.transmission_loss_query_page') }}">
                                            <i class="fas fa-wall-brick me-2"></i>上墙法隔声量查询
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('material_porosity_flow_resistance.query_page') }}">
                                            <i class="fas fa-filter me-2"></i>孔隙率流阻查询
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- 其他数据管理 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#otherDataCollapse">
                                <i class="fas fa-database me-2"></i>其他数据管理
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="otherDataCollapse">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showComingSoon()">
                                            <i class="fas fa-cog me-2"></i>其他数据
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        <!-- 经验数据库 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#experienceCollapse">
                                <i class="fas fa-book me-2"></i>经验数据库
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="experienceCollapse">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showComingSoon()">
                                            <i class="fas fa-lightbulb me-2"></i>经验数据库
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- 标签页导航栏 -->
            {% if session.user %}
            <div class="{% if session.user %}col-md-9 ms-sm-auto col-lg-10{% else %}col-12{% endif %} px-0">
                <div class="tab-navigation-container">
                    <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                        <!-- 标签页将通过JavaScript动态添加 -->
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- 主内容区域 -->
            <main class="{% if session.user %}col-md-9 ms-sm-auto col-lg-10{% else %}col-12{% endif %} px-md-4" id="mainContent">
                <!-- 标签页内容容器 -->
                <div class="tab-content" id="tabContentContainer">
                    <!-- 首页内容 -->
                    <div class="tab-pane active" id="tab-content-home">
                        <div id="home-content">
                            {% block content %}{% endblock %}
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/request.js') }}"></script>
    <script src="{{ url_for('static', filename='js/base.js') }}"></script>
    <script src="{{ url_for('static', filename='js/airtightness.js') }}"></script>
    <script src="{{ url_for('static', filename='js/sound_insulation.js') }}"></script>
    <script src="{{ url_for('static', filename='js/sound_absorption.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
