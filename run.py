#!/usr/bin/env python3
"""
应用启动脚本
"""

import os
import sys
from app import app

def main():
    """主函数"""
    # 获取环境变量
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = True
    
    print(f"启动NVH数据管理系统...")
    print(f"地址: http://{host}:{port}")
    print(f"调试模式: {debug}")
    
    try:
        # 启动应用
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
