from typing import Any, Optional
from flask import jsonify

class ApiResult:
    """统一API返回结果类"""
    
    def __init__(self, code: int, message: str, data: Any = None):
        self.code = code
        self.message = message
        self.data = data
    
    def to_dict(self):
        """转换为字典"""
        result = {
            'code': self.code,
            'message': self.message
        }
        if self.data is not None:
            result['data'] = self.data
        return result
    
    def to_json(self):
        """转换为JSON响应"""
        return jsonify(self.to_dict())

class ResultBuilder:
    """结果构建器"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> ApiResult:
        """成功结果"""
        return ApiResult(200, message, data)
    
    @staticmethod
    def error(message: str = "操作失败", code: int = 500, data: Any = None) -> ApiResult:
        """错误结果"""
        return ApiResult(code, message, data)
    
    @staticmethod
    def not_found(message: str = "资源不存在") -> ApiResult:
        """资源不存在"""
        return ApiResult(404, message)
    
    @staticmethod
    def bad_request(message: str = "请求参数错误") -> ApiResult:
        """请求参数错误"""
        return ApiResult(400, message)
    
    @staticmethod
    def unauthorized(message: str = "未授权访问") -> ApiResult:
        """未授权"""
        return ApiResult(401, message)
    
    @staticmethod
    def forbidden(message: str = "禁止访问") -> ApiResult:
        """禁止访问"""
        return ApiResult(403, message)

# 便捷方法
def success(data: Any = None, message: str = "操作成功"):
    """返回成功结果"""
    return ResultBuilder.success(data, message).to_json()

def error(message: str = "操作失败", code: int = 500, data: Any = None):
    """返回错误结果"""
    return ResultBuilder.error(message, code, data).to_json()

def not_found(message: str = "资源不存在"):
    """返回资源不存在"""
    return ResultBuilder.not_found(message).to_json()

def bad_request(message: str = "请求参数错误"):
    """返回请求参数错误"""
    return ResultBuilder.bad_request(message).to_json()

def unauthorized(message: str = "未授权访问"):
    """返回未授权"""
    return ResultBuilder.unauthorized(message).to_json()

def forbidden(message: str = "禁止访问"):
    """返回禁止访问"""
    return ResultBuilder.forbidden(message).to_json()
